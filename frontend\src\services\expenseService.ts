import api from './api';
import { Expense } from '../types';

export const getExpenses = async (): Promise<Expense[]> => {
  const response = await api.get('/expenses');
  return response.data;
};

export const createExpense = async (expenseData: { 
  amount: number; 
  description: string; 
  date: string; 
  subcategory_id: string 
}): Promise<Expense> => {
  const response = await api.post('/expenses', expenseData);
  return response.data.expense;
};

export const updateExpense = async (id: string, expenseData: { 
  amount: number; 
  description: string; 
  date: string; 
  subcategory_id: string 
}): Promise<Expense> => {
  const response = await api.put(`/expenses/${id}`, expenseData);
  return response.data.expense;
};

export const deleteExpense = async (id: string): Promise<void> => {
  await api.delete(`/expenses/${id}`);
};

export const getExpense = async (id: string): Promise<Expense> => {
  const response = await api.get(`/expenses/${id}`);
  return response.data;
};
