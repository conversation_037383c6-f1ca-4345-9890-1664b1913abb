{"ast": null, "code": "import api from './api';\nexport const getExpenses = async () => {\n  const response = await api.get('/expenses');\n  return response.data;\n};\nexport const createExpense = async expenseData => {\n  const response = await api.post('/expenses', expenseData);\n  return response.data.expense;\n};\nexport const updateExpense = async (id, expenseData) => {\n  const response = await api.put(`/expenses/${id}`, expenseData);\n  return response.data.expense;\n};\nexport const deleteExpense = async id => {\n  await api.delete(`/expenses/${id}`);\n};\nexport const getExpense = async id => {\n  const response = await api.get(`/expenses/${id}`);\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "getExpenses", "response", "get", "data", "createExpense", "expenseData", "post", "expense", "updateExpense", "id", "put", "deleteExpense", "delete", "getExpense"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/services/expenseService.ts"], "sourcesContent": ["import api from './api';\nimport { Expense } from '../types';\n\nexport const getExpenses = async (): Promise<Expense[]> => {\n  const response = await api.get('/expenses');\n  return response.data;\n};\n\nexport const createExpense = async (expenseData: { \n  amount: number; \n  description: string; \n  date: string; \n  subcategory_id: string \n}): Promise<Expense> => {\n  const response = await api.post('/expenses', expenseData);\n  return response.data.expense;\n};\n\nexport const updateExpense = async (id: string, expenseData: { \n  amount: number; \n  description: string; \n  date: string; \n  subcategory_id: string \n}): Promise<Expense> => {\n  const response = await api.put(`/expenses/${id}`, expenseData);\n  return response.data.expense;\n};\n\nexport const deleteExpense = async (id: string): Promise<void> => {\n  await api.delete(`/expenses/${id}`);\n};\n\nexport const getExpense = async (id: string): Promise<Expense> => {\n  const response = await api.get(`/expenses/${id}`);\n  return response.data;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAGvB,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAgC;EACzD,MAAMC,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,WAAW,CAAC;EAC3C,OAAOD,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAOC,WAKnC,IAAuB;EACtB,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACO,IAAI,CAAC,WAAW,EAAED,WAAW,CAAC;EACzD,OAAOJ,QAAQ,CAACE,IAAI,CAACI,OAAO;AAC9B,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOC,EAAU,EAAEJ,WAK/C,KAAuB;EACtB,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACW,GAAG,CAAC,aAAaD,EAAE,EAAE,EAAEJ,WAAW,CAAC;EAC9D,OAAOJ,QAAQ,CAACE,IAAI,CAACI,OAAO;AAC9B,CAAC;AAED,OAAO,MAAMI,aAAa,GAAG,MAAOF,EAAU,IAAoB;EAChE,MAAMV,GAAG,CAACa,MAAM,CAAC,aAAaH,EAAE,EAAE,CAAC;AACrC,CAAC;AAED,OAAO,MAAMI,UAAU,GAAG,MAAOJ,EAAU,IAAuB;EAChE,MAAMR,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,aAAaO,EAAE,EAAE,CAAC;EACjD,OAAOR,QAAQ,CAACE,IAAI;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}