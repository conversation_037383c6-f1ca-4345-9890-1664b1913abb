{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\Simple_1\\\\Simple_flask_react\\\\frontend\\\\src\\\\pages\\\\CategoriesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Header from '../components/Header';\nimport { getCategories, createCategory, deleteCategory } from '../services/categoryService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CategoriesPage = () => {\n  _s();\n  const [categories, setCategories] = useState([]);\n  const [newCategoryName, setNewCategoryName] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadCategories();\n  }, []);\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await getCategories();\n      setCategories(data);\n      setError(null);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateCategory = async e => {\n    e.preventDefault();\n    if (!newCategoryName.trim()) return;\n    try {\n      await createCategory({\n        name: newCategoryName\n      });\n      setNewCategoryName('');\n      loadCategories();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to create category');\n    }\n  };\n  const handleDeleteCategory = async id => {\n    if (!window.confirm('Are you sure you want to delete this category?')) return;\n    try {\n      await deleteCategory(id);\n      loadCategories();\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to delete category');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mx-auto p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"Categories\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-600 text-white p-4 rounded mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 p-6 rounded-lg shadow-md mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Add New Category\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleCreateCategory,\n          className: \"flex gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCategoryName,\n            onChange: e => setNewCategoryName(e.target.value),\n            placeholder: \"Category name\",\n            className: \"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded\",\n            children: \"Add Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Your Categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading categories...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this) : categories.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No categories yet. Create your first category above.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center bg-gray-700 p-3 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleDeleteCategory(category.id),\n              className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\",\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 19\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(CategoriesPage, \"splKqdJSFb7wgJKnNyS8pe8g/HI=\");\n_c = CategoriesPage;\nexport default CategoriesPage;\nvar _c;\n$RefreshReg$(_c, \"CategoriesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "getCategories", "createCategory", "deleteCategory", "jsxDEV", "_jsxDEV", "CategoriesPage", "_s", "categories", "setCategories", "newCategoryName", "setNewCategoryName", "loading", "setLoading", "error", "setError", "loadCategories", "data", "err", "_err$response", "_err$response$data", "response", "message", "handleCreateCategory", "e", "preventDefault", "trim", "name", "_err$response2", "_err$response2$data", "handleDeleteCategory", "id", "window", "confirm", "_err$response3", "_err$response3$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "length", "map", "category", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/pages/CategoriesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Header from '../components/Header';\nimport { getCategories, createCategory, deleteCategory } from '../services/categoryService';\nimport { Category } from '../types';\n\nconst CategoriesPage: React.FC = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [newCategoryName, setNewCategoryName] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadCategories = async () => {\n    try {\n      setLoading(true);\n      const data = await getCategories();\n      setCategories(data);\n      setError(null);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to load categories');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateCategory = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newCategoryName.trim()) return;\n\n    try {\n      await createCategory({ name: newCategoryName });\n      setNewCategoryName('');\n      loadCategories();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create category');\n    }\n  };\n\n  const handleDeleteCategory = async (id: string) => {\n    if (!window.confirm('Are you sure you want to delete this category?')) return;\n\n    try {\n      await deleteCategory(id);\n      loadCategories();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to delete category');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      <Header />\n      <main className=\"container mx-auto p-4\">\n        <h1 className=\"text-3xl font-bold mb-4\">Categories</h1>\n\n        {error && (\n          <div className=\"bg-red-600 text-white p-4 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        {/* Add new category form */}\n        <div className=\"bg-gray-800 p-6 rounded-lg shadow-md mb-8\">\n          <h2 className=\"text-xl font-semibold mb-4\">Add New Category</h2>\n          <form onSubmit={handleCreateCategory} className=\"flex gap-4\">\n            <input\n              type=\"text\"\n              value={newCategoryName}\n              onChange={(e) => setNewCategoryName(e.target.value)}\n              placeholder=\"Category name\"\n              className=\"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n              required\n            />\n            <button\n              type=\"submit\"\n              className=\"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded\"\n            >\n              Add Category\n            </button>\n          </form>\n        </div>\n\n        {/* Categories list */}\n        <div className=\"bg-gray-800 p-6 rounded-lg shadow-md\">\n          <h2 className=\"text-xl font-semibold mb-4\">Your Categories</h2>\n          {loading ? (\n            <p>Loading categories...</p>\n          ) : categories.length === 0 ? (\n            <p>No categories yet. Create your first category above.</p>\n          ) : (\n            <div className=\"space-y-2\">\n              {categories.map((category) => (\n                <div key={category.id} className=\"flex justify-between items-center bg-gray-700 p-3 rounded\">\n                  <span>{category.name}</span>\n                  <button\n                    onClick={() => handleDeleteCategory(category.id)}\n                    className=\"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\"\n                  >\n                    Delete\n                  </button>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default CategoriesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,aAAa,EAAEC,cAAc,EAAEC,cAAc,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG5F,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdiB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,IAAI,GAAG,MAAMhB,aAAa,CAAC,CAAC;MAClCQ,aAAa,CAACQ,IAAI,CAAC;MACnBF,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOG,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBL,QAAQ,CAAC,EAAAI,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBE,OAAO,KAAI,2BAA2B,CAAC;IACtE,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,oBAAoB,GAAG,MAAOC,CAAkB,IAAK;IACzDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACf,eAAe,CAACgB,IAAI,CAAC,CAAC,EAAE;IAE7B,IAAI;MACF,MAAMxB,cAAc,CAAC;QAAEyB,IAAI,EAAEjB;MAAgB,CAAC,CAAC;MAC/CC,kBAAkB,CAAC,EAAE,CAAC;MACtBK,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOE,GAAQ,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACjBd,QAAQ,CAAC,EAAAa,cAAA,GAAAV,GAAG,CAACG,QAAQ,cAAAO,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,uBAAlBA,mBAAA,CAAoBP,OAAO,KAAI,2BAA2B,CAAC;IACtE;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAG,MAAOC,EAAU,IAAK;IACjD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;IAEvE,IAAI;MACF,MAAM9B,cAAc,CAAC4B,EAAE,CAAC;MACxBf,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOE,GAAQ,EAAE;MAAA,IAAAgB,cAAA,EAAAC,mBAAA;MACjBpB,QAAQ,CAAC,EAAAmB,cAAA,GAAAhB,GAAG,CAACG,QAAQ,cAAAa,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBb,OAAO,KAAI,2BAA2B,CAAC;IACtE;EACF,CAAC;EAED,oBACEjB,OAAA;IAAK+B,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClDhC,OAAA,CAACL,MAAM;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVpC,OAAA;MAAM+B,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACrChC,OAAA;QAAI+B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEtD3B,KAAK,iBACJT,OAAA;QAAK+B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EACpDvB;MAAK;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDpC,OAAA;QAAK+B,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDhC,OAAA;UAAI+B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChEpC,OAAA;UAAMqC,QAAQ,EAAEnB,oBAAqB;UAACa,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC1DhC,OAAA;YACEsC,IAAI,EAAC,MAAM;YACXC,KAAK,EAAElC,eAAgB;YACvBmC,QAAQ,EAAGrB,CAAC,IAAKb,kBAAkB,CAACa,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;YACpDG,WAAW,EAAC,eAAe;YAC3BX,SAAS,EAAC,wEAAwE;YAClFY,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFpC,OAAA;YACEsC,IAAI,EAAC,QAAQ;YACbP,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EACnF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNpC,OAAA;QAAK+B,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDhC,OAAA;UAAI+B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC9D7B,OAAO,gBACNP,OAAA;UAAAgC,QAAA,EAAG;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GAC1BjC,UAAU,CAACyC,MAAM,KAAK,CAAC,gBACzB5C,OAAA;UAAAgC,QAAA,EAAG;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAE3DpC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7B,UAAU,CAAC0C,GAAG,CAAEC,QAAQ,iBACvB9C,OAAA;YAAuB+B,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBAC1FhC,OAAA;cAAAgC,QAAA,EAAOc,QAAQ,CAACxB;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BpC,OAAA;cACE+C,OAAO,EAAEA,CAAA,KAAMtB,oBAAoB,CAACqB,QAAQ,CAACpB,EAAE,CAAE;cACjDK,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAPDU,QAAQ,CAACpB,EAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1GID,cAAwB;AAAA+C,EAAA,GAAxB/C,cAAwB;AA4G9B,eAAeA,cAAc;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}