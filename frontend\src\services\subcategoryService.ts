import api from './api';
import { Subcategory } from '../types';

export const getSubcategories = async (): Promise<Subcategory[]> => {
  const response = await api.get('/subcategories');
  return response.data;
};

export const createSubcategory = async (subcategoryData: { name: string; category_id: string }): Promise<Subcategory> => {
  const response = await api.post('/subcategories', subcategoryData);
  return response.data.subcategory;
};

export const updateSubcategory = async (id: string, subcategoryData: { name: string; category_id: string }): Promise<Subcategory> => {
  const response = await api.put(`/subcategories/${id}`, subcategoryData);
  return response.data.subcategory;
};

export const deleteSubcategory = async (id: string): Promise<void> => {
  await api.delete(`/subcategories/${id}`);
};

export const getSubcategory = async (id: string): Promise<Subcategory> => {
  const response = await api.get(`/subcategories/${id}`);
  return response.data;
};
