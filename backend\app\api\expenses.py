from flask import Blueprint, request, jsonify
from app import db
from app.models.models import Expense, Subcategory, Category
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime

bp = Blueprint('expenses', __name__)

@bp.route('', methods=['POST'])
@jwt_required()
def create_expense():
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    if not data or not 'amount' in data or not 'subcategory_id' in data:
        return jsonify({'message': 'Missing data'}), 400

    # Verify that the subcategory belongs to the current user
    subcategory = Subcategory.query.join(Category).filter(Category.user_id == current_user_id, Subcategory.id == data['subcategory_id']).first()
    if not subcategory:
        return jsonify({'message': 'Subcategory not found or does not belong to user'}), 404

    date_str = data.get('date')
    date_obj = datetime.fromisoformat(date_str) if date_str else datetime.utcnow()

    expense = Expense(
        amount=data['amount'],
        description=data.get('description'),
        date=date_obj,
        user_id=current_user_id,
        subcategory_id=data['subcategory_id']
    )
    db.session.add(expense)
    db.session.commit()

    return jsonify({'message': 'Expense created', 'expense': {'id': expense.id, 'amount': expense.amount, 'description': expense.description, 'date': expense.date.isoformat()}}), 201

@bp.route('', methods=['GET'])
@jwt_required()
def get_expenses():
    current_user_id = int(get_jwt_identity())
    expenses = Expense.query.filter_by(user_id=current_user_id).order_by(Expense.date.desc()).all()
    return jsonify([{'id': e.id, 'amount': e.amount, 'description': e.description, 'date': e.date.isoformat(), 'subcategory_id': e.subcategory_id} for e in expenses])

@bp.route('/<int:id>', methods=['GET'])
@jwt_required()
def get_expense(id):
    current_user_id = int(get_jwt_identity())
    expense = Expense.query.filter_by(id=id, user_id=current_user_id).first_or_404()
    return jsonify({'id': expense.id, 'amount': expense.amount, 'description': expense.description, 'date': expense.date.isoformat(), 'subcategory_id': expense.subcategory_id})

@bp.route('/<int:id>', methods=['PUT'])
@jwt_required()
def update_expense(id):
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    expense = Expense.query.filter_by(id=id, user_id=current_user_id).first_or_404()

    if 'amount' in data:
        expense.amount = data['amount']
    if 'description' in data:
        expense.description = data['description']
    if 'date' in data:
        expense.date = datetime.fromisoformat(data['date'])
    if 'subcategory_id' in data:
        # Verify that the new subcategory belongs to the user
        subcategory = Subcategory.query.join(Category).filter(Category.user_id == current_user_id, Subcategory.id == data['subcategory_id']).first()
        if not subcategory:
            return jsonify({'message': 'Subcategory not found or does not belong to user'}), 404
        expense.subcategory_id = data['subcategory_id']

    db.session.commit()

    return jsonify({'message': 'Expense updated', 'expense': {'id': expense.id, 'amount': expense.amount, 'description': expense.description, 'date': expense.date.isoformat()}})

@bp.route('/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_expense(id):
    current_user_id = int(get_jwt_identity())
    expense = Expense.query.filter_by(id=id, user_id=current_user_id).first_or_404()

    db.session.delete(expense)
    db.session.commit()

    return jsonify({'message': 'Expense deleted'})
