{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\Simple_1\\\\Simple_flask_react\\\\frontend\\\\src\\\\pages\\\\ExpensesPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Header from '../components/Header';\nimport { getExpenses, createExpense, deleteExpense } from '../services/expenseService';\nimport { getSubcategories } from '../services/subcategoryService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpensesPage = () => {\n  _s();\n  const [expenses, setExpenses] = useState([]);\n  const [subcategories, setSubcategories] = useState([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newExpense, setNewExpense] = useState({\n    amount: '',\n    description: '',\n    date: new Date().toISOString().split('T')[0],\n    subcategory_id: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    loadData();\n  }, []);\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [expensesData, subcategoriesData] = await Promise.all([getExpenses(), getSubcategories()]);\n      setExpenses(expensesData);\n      setSubcategories(subcategoriesData);\n      setError(null);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreateExpense = async e => {\n    e.preventDefault();\n    if (!newExpense.amount || !newExpense.subcategory_id) return;\n    try {\n      await createExpense({\n        amount: parseFloat(newExpense.amount),\n        description: newExpense.description,\n        date: newExpense.date,\n        subcategory_id: newExpense.subcategory_id\n      });\n      setNewExpense({\n        amount: '',\n        description: '',\n        date: new Date().toISOString().split('T')[0],\n        subcategory_id: ''\n      });\n      setShowAddForm(false);\n      loadData();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || 'Failed to create expense');\n    }\n  };\n  const handleDeleteExpense = async id => {\n    if (!window.confirm('Are you sure you want to delete this expense?')) return;\n    try {\n      await deleteExpense(id);\n      loadData();\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.message) || 'Failed to delete expense');\n    }\n  };\n  const getSubcategoryName = subcategoryId => {\n    const subcategory = subcategories.find(s => s.id === subcategoryId);\n    return subcategory ? subcategory.name : 'Unknown';\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"container mx-auto p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold mb-4\",\n        children: \"Expenses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-600 text-white p-4 rounded mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowAddForm(!showAddForm),\n          className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n          children: showAddForm ? 'Cancel' : 'Add New Expense'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), showAddForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 p-6 rounded-lg shadow-md mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Add New Expense\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleCreateExpense,\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                step: \"0.01\",\n                value: newExpense.amount,\n                onChange: e => setNewExpense({\n                  ...newExpense,\n                  amount: e.target.value\n                }),\n                placeholder: \"0.00\",\n                className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium mb-2\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: newExpense.date,\n                onChange: e => setNewExpense({\n                  ...newExpense,\n                  date: e.target.value\n                }),\n                className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-2\",\n              children: \"Subcategory\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: newExpense.subcategory_id,\n              onChange: e => setNewExpense({\n                ...newExpense,\n                subcategory_id: e.target.value\n              }),\n              className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a subcategory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), subcategories.map(subcategory => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subcategory.id,\n                children: subcategory.name\n              }, subcategory.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium mb-2\",\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: newExpense.description,\n              onChange: e => setNewExpense({\n                ...newExpense,\n                description: e.target.value\n              }),\n              placeholder: \"Expense description\",\n              className: \"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded\",\n            children: \"Add Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 p-6 rounded-lg shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Your Expenses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading expenses...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this) : expenses.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No expenses yet. Add your first expense above.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"pb-2\",\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"pb-2\",\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"pb-2\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"pb-2\",\n                  children: \"Subcategory\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"pb-2\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: expenses.map(expense => /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b border-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"py-2\",\n                  children: formatDate(expense.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"py-2\",\n                  children: [\"$\", expense.amount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"py-2\",\n                  children: expense.description || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"py-2\",\n                  children: getSubcategoryName(expense.subcategory_id)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleDeleteExpense(expense.id),\n                    className: \"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-sm\",\n                    children: \"Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpensesPage, \"q861viiko+Hw/Xjw7L3m6w05CvY=\");\n_c = ExpensesPage;\nexport default ExpensesPage;\nvar _c;\n$RefreshReg$(_c, \"ExpensesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Header", "getExpenses", "createExpense", "deleteExpense", "getSubcategories", "jsxDEV", "_jsxDEV", "ExpensesPage", "_s", "expenses", "setExpenses", "subcategories", "setSubcategories", "showAddForm", "setShowAddForm", "newExpense", "setNewExpense", "amount", "description", "date", "Date", "toISOString", "split", "subcategory_id", "loading", "setLoading", "error", "setError", "loadData", "expensesData", "subcategoriesData", "Promise", "all", "err", "_err$response", "_err$response$data", "response", "data", "message", "handleCreateExpense", "e", "preventDefault", "parseFloat", "_err$response2", "_err$response2$data", "handleDeleteExpense", "id", "window", "confirm", "_err$response3", "_err$response3$data", "getSubcategoryName", "subcategoryId", "subcategory", "find", "s", "name", "formatDate", "dateString", "toLocaleDateString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "type", "step", "value", "onChange", "target", "placeholder", "required", "map", "length", "expense", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/pages/ExpensesPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Header from '../components/Header';\nimport { getExpenses, createExpense, deleteExpense } from '../services/expenseService';\nimport { getSubcategories } from '../services/subcategoryService';\nimport { Expense, Subcategory } from '../types';\n\nconst ExpensesPage: React.FC = () => {\n  const [expenses, setExpenses] = useState<Expense[]>([]);\n  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newExpense, setNewExpense] = useState({\n    amount: '',\n    description: '',\n    date: new Date().toISOString().split('T')[0],\n    subcategory_id: ''\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [expensesData, subcategoriesData] = await Promise.all([\n        getExpenses(),\n        getSubcategories()\n      ]);\n      setExpenses(expensesData);\n      setSubcategories(subcategoriesData);\n      setError(null);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to load data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateExpense = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newExpense.amount || !newExpense.subcategory_id) return;\n\n    try {\n      await createExpense({\n        amount: parseFloat(newExpense.amount),\n        description: newExpense.description,\n        date: newExpense.date,\n        subcategory_id: newExpense.subcategory_id\n      });\n      setNewExpense({\n        amount: '',\n        description: '',\n        date: new Date().toISOString().split('T')[0],\n        subcategory_id: ''\n      });\n      setShowAddForm(false);\n      loadData();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create expense');\n    }\n  };\n\n  const handleDeleteExpense = async (id: string) => {\n    if (!window.confirm('Are you sure you want to delete this expense?')) return;\n\n    try {\n      await deleteExpense(id);\n      loadData();\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to delete expense');\n    }\n  };\n\n  const getSubcategoryName = (subcategoryId: string) => {\n    const subcategory = subcategories.find(s => s.id === subcategoryId);\n    return subcategory ? subcategory.name : 'Unknown';\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      <Header />\n      <main className=\"container mx-auto p-4\">\n        <h1 className=\"text-3xl font-bold mb-4\">Expenses</h1>\n\n        {error && (\n          <div className=\"bg-red-600 text-white p-4 rounded mb-4\">\n            {error}\n          </div>\n        )}\n\n        {/* Add expense button */}\n        <div className=\"mb-6\">\n          <button\n            onClick={() => setShowAddForm(!showAddForm)}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\"\n          >\n            {showAddForm ? 'Cancel' : 'Add New Expense'}\n          </button>\n        </div>\n\n        {/* Add expense form */}\n        {showAddForm && (\n          <div className=\"bg-gray-800 p-6 rounded-lg shadow-md mb-8\">\n            <h2 className=\"text-xl font-semibold mb-4\">Add New Expense</h2>\n            <form onSubmit={handleCreateExpense} className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Amount</label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={newExpense.amount}\n                    onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}\n                    placeholder=\"0.00\"\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\">Date</label>\n                  <input\n                    type=\"date\"\n                    value={newExpense.date}\n                    onChange={(e) => setNewExpense({...newExpense, date: e.target.value})}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n                    required\n                  />\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Subcategory</label>\n                <select\n                  value={newExpense.subcategory_id}\n                  onChange={(e) => setNewExpense({...newExpense, subcategory_id: e.target.value})}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n                  required\n                >\n                  <option value=\"\">Select a subcategory</option>\n                  {subcategories.map((subcategory) => (\n                    <option key={subcategory.id} value={subcategory.id}>\n                      {subcategory.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Description</label>\n                <input\n                  type=\"text\"\n                  value={newExpense.description}\n                  onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}\n                  placeholder=\"Expense description\"\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white\"\n                />\n              </div>\n              <button\n                type=\"submit\"\n                className=\"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded\"\n              >\n                Add Expense\n              </button>\n            </form>\n          </div>\n        )}\n\n        {/* Expenses list */}\n        <div className=\"bg-gray-800 p-6 rounded-lg shadow-md\">\n          <h2 className=\"text-xl font-semibold mb-4\">Your Expenses</h2>\n          {loading ? (\n            <p>Loading expenses...</p>\n          ) : expenses.length === 0 ? (\n            <p>No expenses yet. Add your first expense above.</p>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full text-left\">\n                <thead>\n                  <tr className=\"border-b border-gray-600\">\n                    <th className=\"pb-2\">Date</th>\n                    <th className=\"pb-2\">Amount</th>\n                    <th className=\"pb-2\">Description</th>\n                    <th className=\"pb-2\">Subcategory</th>\n                    <th className=\"pb-2\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {expenses.map((expense) => (\n                    <tr key={expense.id} className=\"border-b border-gray-700\">\n                      <td className=\"py-2\">{formatDate(expense.date)}</td>\n                      <td className=\"py-2\">${expense.amount.toFixed(2)}</td>\n                      <td className=\"py-2\">{expense.description || '-'}</td>\n                      <td className=\"py-2\">{getSubcategoryName(expense.subcategory_id)}</td>\n                      <td className=\"py-2\">\n                        <button\n                          onClick={() => handleDeleteExpense(expense.id)}\n                          className=\"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-sm\"\n                        >\n                          Delete\n                        </button>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n};\n\nexport default ExpensesPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,WAAW,EAAEC,aAAa,EAAEC,aAAa,QAAQ,4BAA4B;AACtF,SAASC,gBAAgB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlE,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EACrE,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC;IAC3CmB,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5CC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd6B,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACI,YAAY,EAAEC,iBAAiB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1D/B,WAAW,CAAC,CAAC,EACbG,gBAAgB,CAAC,CAAC,CACnB,CAAC;MACFM,WAAW,CAACmB,YAAY,CAAC;MACzBjB,gBAAgB,CAACkB,iBAAiB,CAAC;MACnCH,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOM,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBR,QAAQ,CAAC,EAAAO,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBG,OAAO,KAAI,qBAAqB,CAAC;IAChE,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC1B,UAAU,CAACE,MAAM,IAAI,CAACF,UAAU,CAACQ,cAAc,EAAE;IAEtD,IAAI;MACF,MAAMrB,aAAa,CAAC;QAClBe,MAAM,EAAEyB,UAAU,CAAC3B,UAAU,CAACE,MAAM,CAAC;QACrCC,WAAW,EAAEH,UAAU,CAACG,WAAW;QACnCC,IAAI,EAAEJ,UAAU,CAACI,IAAI;QACrBI,cAAc,EAAER,UAAU,CAACQ;MAC7B,CAAC,CAAC;MACFP,aAAa,CAAC;QACZC,MAAM,EAAE,EAAE;QACVC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5CC,cAAc,EAAE;MAClB,CAAC,CAAC;MACFT,cAAc,CAAC,KAAK,CAAC;MACrBc,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOK,GAAQ,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACjBjB,QAAQ,CAAC,EAAAgB,cAAA,GAAAV,GAAG,CAACG,QAAQ,cAAAO,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcN,IAAI,cAAAO,mBAAA,uBAAlBA,mBAAA,CAAoBN,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMO,mBAAmB,GAAG,MAAOC,EAAU,IAAK;IAChD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;IAEtE,IAAI;MACF,MAAM7C,aAAa,CAAC2C,EAAE,CAAC;MACvBlB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOK,GAAQ,EAAE;MAAA,IAAAgB,cAAA,EAAAC,mBAAA;MACjBvB,QAAQ,CAAC,EAAAsB,cAAA,GAAAhB,GAAG,CAACG,QAAQ,cAAAa,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBZ,OAAO,KAAI,0BAA0B,CAAC;IACrE;EACF,CAAC;EAED,MAAMa,kBAAkB,GAAIC,aAAqB,IAAK;IACpD,MAAMC,WAAW,GAAG1C,aAAa,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACT,EAAE,KAAKM,aAAa,CAAC;IACnE,OAAOC,WAAW,GAAGA,WAAW,CAACG,IAAI,GAAG,SAAS;EACnD,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAkB,IAAK;IACzC,OAAO,IAAItC,IAAI,CAACsC,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,oBACErD,OAAA;IAAKsD,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAClDvD,OAAA,CAACN,MAAM;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV3D,OAAA;MAAMsD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACrCvD,OAAA;QAAIsD,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEpDvC,KAAK,iBACJpB,OAAA;QAAKsD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EACpDnC;MAAK;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD3D,OAAA;QAAKsD,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBvD,OAAA;UACE4D,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5C+C,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAE/EhD,WAAW,GAAG,QAAQ,GAAG;QAAiB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLpD,WAAW,iBACVP,OAAA;QAAKsD,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDvD,OAAA;UAAIsD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/D3D,OAAA;UAAM6D,QAAQ,EAAE5B,mBAAoB;UAACqB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxDvD,OAAA;YAAKsD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDvD,OAAA;cAAAuD,QAAA,gBACEvD,OAAA;gBAAOsD,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChE3D,OAAA;gBACE8D,IAAI,EAAC,QAAQ;gBACbC,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAEvD,UAAU,CAACE,MAAO;gBACzBsD,QAAQ,EAAG/B,CAAC,IAAKxB,aAAa,CAAC;kBAAC,GAAGD,UAAU;kBAAEE,MAAM,EAAEuB,CAAC,CAACgC,MAAM,CAACF;gBAAK,CAAC,CAAE;gBACxEG,WAAW,EAAC,MAAM;gBAClBb,SAAS,EAAC,wEAAwE;gBAClFc,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3D,OAAA;cAAAuD,QAAA,gBACEvD,OAAA;gBAAOsD,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9D3D,OAAA;gBACE8D,IAAI,EAAC,MAAM;gBACXE,KAAK,EAAEvD,UAAU,CAACI,IAAK;gBACvBoD,QAAQ,EAAG/B,CAAC,IAAKxB,aAAa,CAAC;kBAAC,GAAGD,UAAU;kBAAEI,IAAI,EAAEqB,CAAC,CAACgC,MAAM,CAACF;gBAAK,CAAC,CAAE;gBACtEV,SAAS,EAAC,wEAAwE;gBAClFc,QAAQ;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3D,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAOsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrE3D,OAAA;cACEgE,KAAK,EAAEvD,UAAU,CAACQ,cAAe;cACjCgD,QAAQ,EAAG/B,CAAC,IAAKxB,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEQ,cAAc,EAAEiB,CAAC,CAACgC,MAAM,CAACF;cAAK,CAAC,CAAE;cAChFV,SAAS,EAAC,wEAAwE;cAClFc,QAAQ;cAAAb,QAAA,gBAERvD,OAAA;gBAAQgE,KAAK,EAAC,EAAE;gBAAAT,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC7CtD,aAAa,CAACgE,GAAG,CAAEtB,WAAW,iBAC7B/C,OAAA;gBAA6BgE,KAAK,EAAEjB,WAAW,CAACP,EAAG;gBAAAe,QAAA,EAChDR,WAAW,CAACG;cAAI,GADNH,WAAW,CAACP,EAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN3D,OAAA;YAAAuD,QAAA,gBACEvD,OAAA;cAAOsD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrE3D,OAAA;cACE8D,IAAI,EAAC,MAAM;cACXE,KAAK,EAAEvD,UAAU,CAACG,WAAY;cAC9BqD,QAAQ,EAAG/B,CAAC,IAAKxB,aAAa,CAAC;gBAAC,GAAGD,UAAU;gBAAEG,WAAW,EAAEsB,CAAC,CAACgC,MAAM,CAACF;cAAK,CAAC,CAAE;cAC7EG,WAAW,EAAC,qBAAqB;cACjCb,SAAS,EAAC;YAAwE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3D,OAAA;YACE8D,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,wEAAwE;YAAAC,QAAA,EACnF;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAGD3D,OAAA;QAAKsD,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDvD,OAAA;UAAIsD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5DzC,OAAO,gBACNlB,OAAA;UAAAuD,QAAA,EAAG;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,GACxBxD,QAAQ,CAACmE,MAAM,KAAK,CAAC,gBACvBtE,OAAA;UAAAuD,QAAA,EAAG;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAErD3D,OAAA;UAAKsD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BvD,OAAA;YAAOsD,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBACjCvD,OAAA;cAAAuD,QAAA,eACEvD,OAAA;gBAAIsD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACtCvD,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9B3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChC3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrC3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR3D,OAAA;cAAAuD,QAAA,EACGpD,QAAQ,CAACkE,GAAG,CAAEE,OAAO,iBACpBvE,OAAA;gBAAqBsD,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvDvD,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEJ,UAAU,CAACoB,OAAO,CAAC1D,IAAI;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpD3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,GAAC,GAAC,EAACgB,OAAO,CAAC5D,MAAM,CAAC6D,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtD3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEgB,OAAO,CAAC3D,WAAW,IAAI;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtD3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEV,kBAAkB,CAAC0B,OAAO,CAACtD,cAAc;gBAAC;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtE3D,OAAA;kBAAIsD,SAAS,EAAC,MAAM;kBAAAC,QAAA,eAClBvD,OAAA;oBACE4D,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACgC,OAAO,CAAC/B,EAAE,CAAE;oBAC/Cc,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,EAC7E;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAZEY,OAAO,CAAC/B,EAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzD,EAAA,CAjNID,YAAsB;AAAAwE,EAAA,GAAtBxE,YAAsB;AAmN5B,eAAeA,YAAY;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}