from flask import Flask, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager
from config import Config
from flask_admin import Admin

db = SQLAlchemy()
migrate = Migrate()
cors = CORS()
jwt = JWTManager()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    cors.init_app(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3006", "http://127.0.0.1:3006"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    jwt.init_app(app)

    # Import models here to make sure they are known to Flask-Migrate
    from app.models.models import User, Category, Subcategory, Expense
    from app import models

    # Import admin views and initialize admin after models are loaded
    from app.admin.views import UserAdminView, CategoryAdminView, SubcategoryAdminView, ExpenseAdminView
    admin = Admin(app, name='Expense Manager', template_mode='bootstrap3')

    # Add admin views
    admin.add_view(UserAdminView(User, db.session))
    admin.add_view(CategoryAdminView(Category, db.session))
    admin.add_view(SubcategoryAdminView(Subcategory, db.session))
    admin.add_view(ExpenseAdminView(Expense, db.session))

    # Register blueprints
    from app.api.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/api/auth')

    from app.api.categories import bp as categories_bp
    app.register_blueprint(categories_bp, url_prefix='/api/categories')

    from app.api.subcategories import bp as subcategories_bp
    app.register_blueprint(subcategories_bp, url_prefix='/api/subcategories')

    from app.api.expenses import bp as expenses_bp
    app.register_blueprint(expenses_bp, url_prefix='/api/expenses')

    from app.api.dashboard import bp as dashboard_bp
    app.register_blueprint(dashboard_bp, url_prefix='/api/dashboard')

    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'message': 'Resource not found'}), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'message': 'Internal server error'}), 500

    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'message': 'Bad request'}), 400

    with app.app_context():
        # This is a good place to create tables if they don't exist
        # but with Flask-Migrate, we'll use migrations instead.
        pass

    return app