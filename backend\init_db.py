#!/usr/bin/env python3
"""
Script pour initialiser la base de données
"""
from app import create_app, db
from app.models.models import User, Category, Subcategory, Expense

def init_database():
    """Initialise la base de données avec les tables"""
    app = create_app()
    
    with app.app_context():
        # C<PERSON>er toutes les tables
        db.create_all()
        print("Base de données initialisée avec succès!")
        
        # Optionnel: créer un utilisateur de test
        test_user = User.query.filter_by(username='admin').first()
        if not test_user:
            test_user = User(username='admin', email='<EMAIL>')
            test_user.set_password('admin123')
            db.session.add(test_user)
            db.session.commit()
            print("Utilisateur de test créé: admin/admin123")

if __name__ == '__main__':
    init_database()
