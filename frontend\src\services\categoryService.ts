import api from './api';
import { Category } from '../types';

export const getCategories = async (): Promise<Category[]> => {
  const response = await api.get('/categories');
  return response.data;
};

export const createCategory = async (categoryData: { name: string }): Promise<Category> => {
  const response = await api.post('/categories', categoryData);
  return response.data.category;
};

export const updateCategory = async (id: string, categoryData: { name: string }): Promise<Category> => {
  const response = await api.put(`/categories/${id}`, categoryData);
  return response.data.category;
};

export const deleteCategory = async (id: string): Promise<void> => {
  await api.delete(`/categories/${id}`);
};

export const getCategory = async (id: string): Promise<Category> => {
  const response = await api.get(`/categories/${id}`);
  return response.data;
};
