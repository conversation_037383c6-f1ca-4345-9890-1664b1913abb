import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import { getSubcategories, createSubcategory, deleteSubcategory } from '../services/subcategoryService';
import { getCategories } from '../services/categoryService';
import { Subcategory, Category } from '../types';

const SubcategoriesPage: React.FC = () => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [subcategoriesData, categoriesData] = await Promise.all([
        getSubcategories(),
        getCategories()
      ]);
      setSubcategories(subcategoriesData);
      setCategories(categoriesData);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSubcategory = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newSubcategoryName.trim() || !selectedCategoryId) return;

    try {
      await createSubcategory({
        name: newSubcategoryName,
        category_id: selectedCategoryId
      });
      setNewSubcategoryName('');
      setSelectedCategoryId('');
      loadData();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create subcategory');
    }
  };

  const handleDeleteSubcategory = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this subcategory?')) return;

    try {
      await deleteSubcategory(id);
      loadData();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete subcategory');
    }
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : 'Unknown';
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header />
      <main className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-4">Subcategories</h1>

        {error && (
          <div className="bg-red-600 text-white p-4 rounded mb-4">
            {error}
          </div>
        )}

        {/* Add new subcategory form */}
        <div className="bg-gray-800 p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-semibold mb-4">Add New Subcategory</h2>
          <form onSubmit={handleCreateSubcategory} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                value={selectedCategoryId}
                onChange={(e) => setSelectedCategoryId(e.target.value)}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Subcategory Name</label>
              <input
                type="text"
                value={newSubcategoryName}
                onChange={(e) => setNewSubcategoryName(e.target.value)}
                placeholder="Subcategory name"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                required
              />
            </div>
            <button
              type="submit"
              className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
            >
              Add Subcategory
            </button>
          </form>
        </div>

        {/* Subcategories list */}
        <div className="bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Your Subcategories</h2>
          {loading ? (
            <p>Loading subcategories...</p>
          ) : subcategories.length === 0 ? (
            <p>No subcategories yet. Create your first subcategory above.</p>
          ) : (
            <div className="space-y-2">
              {subcategories.map((subcategory) => (
                <div key={subcategory.id} className="flex justify-between items-center bg-gray-700 p-3 rounded">
                  <div>
                    <span className="font-medium">{subcategory.name}</span>
                    <span className="text-gray-400 ml-2">({getCategoryName(subcategory.category_id)})</span>
                  </div>
                  <button
                    onClick={() => handleDeleteSubcategory(subcategory.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                  >
                    Delete
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default SubcategoriesPage;
