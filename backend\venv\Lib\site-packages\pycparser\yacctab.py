
# yacctab.py
# This file is automatically generated. Do not edit.
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = 'translation_unit_or_emptyleftLORleftLANDleftORleftXORleftANDleftEQNEleftGTGELTLEleftRSHIFTLSHIFTleftPLUSMINUSleftTIMESDIVIDEMODAUTO BREAK CASE CHAR CONST CONTINUE DEFAULT DO DOUBLE ELSE ENUM EXTERN FLOAT FOR GOTO IF INLINE INT LONG REGISTER OFFSETOF RESTRICT RETURN SHORT SIGNED SIZEOF STATIC STRUCT SWITCH TYPEDEF UNION UNSIGNED VOID VOLATILE WHILE __INT128 _BOOL _COMPLEX _NORETURN _THREAD_LOCAL _STATIC_ASSERT _ATOMIC _ALIGNOF _ALIGNAS _PRAGMA ID TYPEID INT_CONST_DEC INT_CONST_OCT INT_CONST_HEX INT_CONST_BIN INT_CONST_CHAR FLOAT_CONST HEX_FLOAT_CONST CHAR_CONST WCHAR_CONST U8CHAR_CONST U16CHAR_CONST U32CHAR_CONST STRING_LITERAL WSTRING_LITERAL U8STRING_LITERAL U16STRING_LITERAL U32STRING_LITERAL PLUS MINUS TIMES DIVIDE MOD OR AND NOT XOR LSHIFT RSHIFT LOR LAND LNOT LT LE GT GE EQ NE EQUALS TIMESEQUAL DIVEQUAL MODEQUAL PLUSEQUAL MINUSEQUAL LSHIFTEQUAL RSHIFTEQUAL ANDEQUAL XOREQUAL OREQUAL PLUSPLUS MINUSMINUS ARROW CONDOP LPAREN RPAREN LBRACKET RBRACKET LBRACE RBRACE COMMA PERIOD SEMI COLON ELLIPSIS PPHASH PPPRAGMA PPPRAGMASTRabstract_declarator_opt : empty\n| abstract_declaratorassignment_expression_opt : empty\n| assignment_expressionblock_item_list_opt : empty\n| block_item_listdeclaration_list_opt : empty\n| declaration_listdeclaration_specifiers_no_type_opt : empty\n| declaration_specifiers_no_typedesignation_opt : empty\n| designationexpression_opt : empty\n| expressionid_init_declarator_list_opt : empty\n| id_init_declarator_listidentifier_list_opt : empty\n| identifier_listinit_declarator_list_opt : empty\n| init_declarator_listinitializer_list_opt : empty\n| initializer_listparameter_type_list_opt : empty\n| parameter_type_liststruct_declarator_list_opt : empty\n| struct_declarator_listtype_qualifier_list_opt : empty\n| type_qualifier_list direct_id_declarator   : ID\n         direct_id_declarator   : LPAREN id_declarator RPAREN\n         direct_id_declarator   : direct_id_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET\n         direct_id_declarator   : direct_id_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET\n                                    | direct_id_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET\n         direct_id_declarator   : direct_id_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET\n         direct_id_declarator   : direct_id_declarator LPAREN parameter_type_list RPAREN\n                                    | direct_id_declarator LPAREN identifier_list_opt RPAREN\n         direct_typeid_declarator   : TYPEID\n         direct_typeid_declarator   : LPAREN typeid_declarator RPAREN\n         direct_typeid_declarator   : direct_typeid_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET\n         direct_typeid_declarator   : direct_typeid_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET\n                                    | direct_typeid_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET\n         direct_typeid_declarator   : direct_typeid_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET\n         direct_typeid_declarator   : direct_typeid_declarator LPAREN parameter_type_list RPAREN\n                                    | direct_typeid_declarator LPAREN identifier_list_opt RPAREN\n         direct_typeid_noparen_declarator   : TYPEID\n         direct_typeid_noparen_declarator   : direct_typeid_noparen_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET\n         direct_typeid_noparen_declarator   : direct_typeid_noparen_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET\n                                    | direct_typeid_noparen_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET\n         direct_typeid_noparen_declarator   : direct_typeid_noparen_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET\n         direct_typeid_noparen_declarator   : direct_typeid_noparen_declarator LPAREN parameter_type_list RPAREN\n                                    | direct_typeid_noparen_declarator LPAREN identifier_list_opt RPAREN\n         id_declarator  : direct_id_declarator\n         id_declarator  : pointer direct_id_declarator\n         typeid_declarator  : direct_typeid_declarator\n         typeid_declarator  : pointer direct_typeid_declarator\n         typeid_noparen_declarator  : direct_typeid_noparen_declarator\n         typeid_noparen_declarator  : pointer direct_typeid_noparen_declarator\n         translation_unit_or_empty   : translation_unit\n                                        | empty\n         translation_unit    : external_declaration\n         translation_unit    : translation_unit external_declaration\n         external_declaration    : function_definition\n         external_declaration    : declaration\n         external_declaration    : pp_directive\n                                    | pppragma_directive\n         external_declaration    : SEMI\n         external_declaration    : static_assert\n         static_assert           : _STATIC_ASSERT LPAREN constant_expression COMMA unified_string_literal RPAREN\n                                    | _STATIC_ASSERT LPAREN constant_expression RPAREN\n         pp_directive  : PPHASH\n         pppragma_directive      : PPPRAGMA\n                                    | PPPRAGMA PPPRAGMASTR\n                                    | _PRAGMA LPAREN unified_string_literal RPAREN\n         pppragma_directive_list : pppragma_directive\n                                    | pppragma_directive_list pppragma_directive\n         function_definition : id_declarator declaration_list_opt compound_statement\n         function_definition : declaration_specifiers id_declarator declaration_list_opt compound_statement\n         statement   : labeled_statement\n                        | expression_statement\n                        | compound_statement\n                        | selection_statement\n                        | iteration_statement\n                        | jump_statement\n                        | pppragma_directive\n                        | static_assert\n         pragmacomp_or_statement     : pppragma_directive_list statement\n                                        | statement\n         decl_body : declaration_specifiers init_declarator_list_opt\n                      | declaration_specifiers_no_type id_init_declarator_list_opt\n         declaration : decl_body SEMI\n         declaration_list    : declaration\n                                | declaration_list declaration\n         declaration_specifiers_no_type  : type_qualifier declaration_specifiers_no_type_opt\n         declaration_specifiers_no_type  : storage_class_specifier declaration_specifiers_no_type_opt\n         declaration_specifiers_no_type  : function_specifier declaration_specifiers_no_type_opt\n         declaration_specifiers_no_type  : atomic_specifier declaration_specifiers_no_type_opt\n         declaration_specifiers_no_type  : alignment_specifier declaration_specifiers_no_type_opt\n         declaration_specifiers  : declaration_specifiers type_qualifier\n         declaration_specifiers  : declaration_specifiers storage_class_specifier\n         declaration_specifiers  : declaration_specifiers function_specifier\n         declaration_specifiers  : declaration_specifiers type_specifier_no_typeid\n         declaration_specifiers  : type_specifier\n         declaration_specifiers  : declaration_specifiers_no_type type_specifier\n         declaration_specifiers  : declaration_specifiers alignment_specifier\n         storage_class_specifier : AUTO\n                                    | REGISTER\n                                    | STATIC\n                                    | EXTERN\n                                    | TYPEDEF\n                                    | _THREAD_LOCAL\n         function_specifier  : INLINE\n                                | _NORETURN\n         type_specifier_no_typeid  : VOID\n                                      | _BOOL\n                                      | CHAR\n                                      | SHORT\n                                      | INT\n                                      | LONG\n                                      | FLOAT\n                                      | DOUBLE\n                                      | _COMPLEX\n                                      | SIGNED\n                                      | UNSIGNED\n                                      | __INT128\n         type_specifier  : typedef_name\n                            | enum_specifier\n                            | struct_or_union_specifier\n                            | type_specifier_no_typeid\n                            | atomic_specifier\n         atomic_specifier  : _ATOMIC LPAREN type_name RPAREN\n         type_qualifier  : CONST\n                            | RESTRICT\n                            | VOLATILE\n                            | _ATOMIC\n         init_declarator_list    : init_declarator\n                                    | init_declarator_list COMMA init_declarator\n         init_declarator : declarator\n                            | declarator EQUALS initializer\n         id_init_declarator_list    : id_init_declarator\n                                       | id_init_declarator_list COMMA init_declarator\n         id_init_declarator : id_declarator\n                               | id_declarator EQUALS initializer\n         specifier_qualifier_list    : specifier_qualifier_list type_specifier_no_typeid\n         specifier_qualifier_list    : specifier_qualifier_list type_qualifier\n         specifier_qualifier_list  : type_specifier\n         specifier_qualifier_list  : type_qualifier_list type_specifier\n         specifier_qualifier_list  : alignment_specifier\n         specifier_qualifier_list  : specifier_qualifier_list alignment_specifier\n         struct_or_union_specifier   : struct_or_union ID\n                                        | struct_or_union TYPEID\n         struct_or_union_specifier : struct_or_union brace_open struct_declaration_list brace_close\n                                      | struct_or_union brace_open brace_close\n         struct_or_union_specifier   : struct_or_union ID brace_open struct_declaration_list brace_close\n                                        | struct_or_union ID brace_open brace_close\n                                        | struct_or_union TYPEID brace_open struct_declaration_list brace_close\n                                        | struct_or_union TYPEID brace_open brace_close\n         struct_or_union : STRUCT\n                            | UNION\n         struct_declaration_list     : struct_declaration\n                                        | struct_declaration_list struct_declaration\n         struct_declaration : specifier_qualifier_list struct_declarator_list_opt SEMI\n         struct_declaration : SEMI\n         struct_declaration : pppragma_directive\n         struct_declarator_list  : struct_declarator\n                                    | struct_declarator_list COMMA struct_declarator\n         struct_declarator : declarator\n         struct_declarator   : declarator COLON constant_expression\n                                | COLON constant_expression\n         enum_specifier  : ENUM ID\n                            | ENUM TYPEID\n         enum_specifier  : ENUM brace_open enumerator_list brace_close\n         enum_specifier  : ENUM ID brace_open enumerator_list brace_close\n                            | ENUM TYPEID brace_open enumerator_list brace_close\n         enumerator_list : enumerator\n                            | enumerator_list COMMA\n                            | enumerator_list COMMA enumerator\n         alignment_specifier  : _ALIGNAS LPAREN type_name RPAREN\n                                 | _ALIGNAS LPAREN constant_expression RPAREN\n         enumerator  : ID\n                        | ID EQUALS constant_expression\n         declarator  : id_declarator\n                        | typeid_declarator\n         pointer : TIMES type_qualifier_list_opt\n                    | TIMES type_qualifier_list_opt pointer\n         type_qualifier_list : type_qualifier\n                                | type_qualifier_list type_qualifier\n         parameter_type_list : parameter_list\n                                | parameter_list COMMA ELLIPSIS\n         parameter_list  : parameter_declaration\n                            | parameter_list COMMA parameter_declaration\n         parameter_declaration   : declaration_specifiers id_declarator\n                                    | declaration_specifiers typeid_noparen_declarator\n         parameter_declaration   : declaration_specifiers abstract_declarator_opt\n         identifier_list : identifier\n                            | identifier_list COMMA identifier\n         initializer : assignment_expression\n         initializer : brace_open initializer_list_opt brace_close\n                        | brace_open initializer_list COMMA brace_close\n         initializer_list    : designation_opt initializer\n                                | initializer_list COMMA designation_opt initializer\n         designation : designator_list EQUALS\n         designator_list : designator\n                            | designator_list designator\n         designator  : LBRACKET constant_expression RBRACKET\n                        | PERIOD identifier\n         type_name   : specifier_qualifier_list abstract_declarator_opt\n         abstract_declarator     : pointer\n         abstract_declarator     : pointer direct_abstract_declarator\n         abstract_declarator     : direct_abstract_declarator\n         direct_abstract_declarator  : LPAREN abstract_declarator RPAREN  direct_abstract_declarator  : direct_abstract_declarator LBRACKET assignment_expression_opt RBRACKET\n         direct_abstract_declarator  : LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET\n         direct_abstract_declarator  : direct_abstract_declarator LBRACKET TIMES RBRACKET\n         direct_abstract_declarator  : LBRACKET TIMES RBRACKET\n         direct_abstract_declarator  : direct_abstract_declarator LPAREN parameter_type_list_opt RPAREN\n         direct_abstract_declarator  : LPAREN parameter_type_list_opt RPAREN\n         direct_abstract_declarator  : LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET\n                                         | LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET\n         block_item  : declaration\n                        | statement\n         block_item_list : block_item\n                            | block_item_list block_item\n         compound_statement : brace_open block_item_list_opt brace_close  labeled_statement : ID COLON pragmacomp_or_statement  labeled_statement : CASE constant_expression COLON pragmacomp_or_statement  labeled_statement : DEFAULT COLON pragmacomp_or_statement  labeled_statement : ID COLON  labeled_statement : CASE constant_expression COLON  labeled_statement : DEFAULT COLON  selection_statement : IF LPAREN expression RPAREN pragmacomp_or_statement  selection_statement : IF LPAREN expression RPAREN statement ELSE pragmacomp_or_statement  selection_statement : SWITCH LPAREN expression RPAREN pragmacomp_or_statement  iteration_statement : WHILE LPAREN expression RPAREN pragmacomp_or_statement  iteration_statement : DO pragmacomp_or_statement WHILE LPAREN expression RPAREN SEMI  iteration_statement : FOR LPAREN expression_opt SEMI expression_opt SEMI expression_opt RPAREN pragmacomp_or_statement  iteration_statement : FOR LPAREN declaration expression_opt SEMI expression_opt RPAREN pragmacomp_or_statement  jump_statement  : GOTO ID SEMI  jump_statement  : BREAK SEMI  jump_statement  : CONTINUE SEMI  jump_statement  : RETURN expression SEMI\n                            | RETURN SEMI\n         expression_statement : expression_opt SEMI  expression  : assignment_expression\n                        | expression COMMA assignment_expression\n         assignment_expression : LPAREN compound_statement RPAREN  typedef_name : TYPEID  assignment_expression   : conditional_expression\n                                    | unary_expression assignment_operator assignment_expression\n         assignment_operator : EQUALS\n                                | XOREQUAL\n                                | TIMESEQUAL\n                                | DIVEQUAL\n                                | MODEQUAL\n                                | PLUSEQUAL\n                                | MINUSEQUAL\n                                | LSHIFTEQUAL\n                                | RSHIFTEQUAL\n                                | ANDEQUAL\n                                | OREQUAL\n         constant_expression : conditional_expression  conditional_expression  : binary_expression\n                                    | binary_expression CONDOP expression COLON conditional_expression\n         binary_expression   : cast_expression\n                                | binary_expression TIMES binary_expression\n                                | binary_expression DIVIDE binary_expression\n                                | binary_expression MOD binary_expression\n                                | binary_expression PLUS binary_expression\n                                | binary_expression MINUS binary_expression\n                                | binary_expression RSHIFT binary_expression\n                                | binary_expression LSHIFT binary_expression\n                                | binary_expression LT binary_expression\n                                | binary_expression LE binary_expression\n                                | binary_expression GE binary_expression\n                                | binary_expression GT binary_expression\n                                | binary_expression EQ binary_expression\n                                | binary_expression NE binary_expression\n                                | binary_expression AND binary_expression\n                                | binary_expression OR binary_expression\n                                | binary_expression XOR binary_expression\n                                | binary_expression LAND binary_expression\n                                | binary_expression LOR binary_expression\n         cast_expression : unary_expression  cast_expression : LPAREN type_name RPAREN cast_expression  unary_expression    : postfix_expression  unary_expression    : PLUSPLUS unary_expression\n                                | MINUSMINUS unary_expression\n                                | unary_operator cast_expression\n         unary_expression    : SIZEOF unary_expression\n                                | SIZEOF LPAREN type_name RPAREN\n                                | _ALIGNOF LPAREN type_name RPAREN\n         unary_operator  : AND\n                            | TIMES\n                            | PLUS\n                            | MINUS\n                            | NOT\n                            | LNOT\n         postfix_expression  : primary_expression  postfix_expression  : postfix_expression LBRACKET expression RBRACKET  postfix_expression  : postfix_expression LPAREN argument_expression_list RPAREN\n                                | postfix_expression LPAREN RPAREN\n         postfix_expression  : postfix_expression PERIOD ID\n                                | postfix_expression PERIOD TYPEID\n                                | postfix_expression ARROW ID\n                                | postfix_expression ARROW TYPEID\n         postfix_expression  : postfix_expression PLUSPLUS\n                                | postfix_expression MINUSMINUS\n         postfix_expression  : LPAREN type_name RPAREN brace_open initializer_list brace_close\n                                | LPAREN type_name RPAREN brace_open initializer_list COMMA brace_close\n         primary_expression  : identifier  primary_expression  : constant  primary_expression  : unified_string_literal\n                                | unified_wstring_literal\n         primary_expression  : LPAREN expression RPAREN  primary_expression  : OFFSETOF LPAREN type_name COMMA offsetof_member_designator RPAREN\n         offsetof_member_designator : identifier\n                                         | offsetof_member_designator PERIOD identifier\n                                         | offsetof_member_designator LBRACKET expression RBRACKET\n         argument_expression_list    : assignment_expression\n                                        | argument_expression_list COMMA assignment_expression\n         identifier  : ID  constant    : INT_CONST_DEC\n                        | INT_CONST_OCT\n                        | INT_CONST_HEX\n                        | INT_CONST_BIN\n                        | INT_CONST_CHAR\n         constant    : FLOAT_CONST\n                        | HEX_FLOAT_CONST\n         constant    : CHAR_CONST\n                        | WCHAR_CONST\n                        | U8CHAR_CONST\n                        | U16CHAR_CONST\n                        | U32CHAR_CONST\n         unified_string_literal  : STRING_LITERAL\n                                    | unified_string_literal STRING_LITERAL\n         unified_wstring_literal : WSTRING_LITERAL\n                                    | U8STRING_LITERAL\n                                    | U16STRING_LITERAL\n                                    | U32STRING_LITERAL\n                                    | unified_wstring_literal WSTRING_LITERAL\n                                    | unified_wstring_literal U8STRING_LITERAL\n                                    | unified_wstring_literal U16STRING_LITERAL\n                                    | unified_wstring_literal U32STRING_LITERAL\n         brace_open  :   LBRACE\n         brace_close :   RBRACE\n        empty : '
    
_lr_action_items = {'$end':([0,1,2,3,4,5,6,7,8,9,10,14,15,64,90,91,127,208,251,262,267,355,501,],[-345,0,-58,-59,-60,-62,-63,-64,-65,-66,-67,-70,-71,-61,-90,-72,-76,-344,-77,-73,-69,-223,-68,]),'SEMI':([0,2,4,5,6,7,8,9,10,12,13,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,69,70,71,72,73,74,75,76,77,78,79,81,83,84,85,86,87,88,89,90,91,97,98,99,100,101,102,103,104,105,106,107,108,110,111,112,117,118,119,121,122,123,124,127,128,130,132,139,140,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,203,204,205,206,207,208,209,210,211,212,214,220,221,222,223,224,225,226,227,228,229,230,231,232,233,236,239,242,245,246,247,248,249,250,251,252,253,254,255,262,263,267,291,292,293,295,296,297,300,301,302,303,311,312,326,327,330,333,334,335,336,337,338,339,340,341,342,343,344,345,346,348,349,353,354,355,356,357,358,360,361,369,370,371,372,373,374,375,376,377,403,404,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,439,440,461,462,465,466,467,470,471,472,473,475,477,481,482,483,484,485,486,487,488,495,496,499,501,503,504,507,508,510,511,526,527,528,529,530,531,533,534,535,539,540,542,558,559,560,561,562,564,567,569,578,579,582,587,588,590,592,593,594,],[9,9,-60,-62,-63,-64,-65,-66,-67,-345,90,-70,-71,-52,-345,-345,-345,-128,-102,-345,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,-345,-345,-129,-134,-181,-98,-99,-100,-101,-104,-88,-134,-19,-20,-135,-137,-182,-54,-37,-90,-72,-53,-93,-9,-10,-345,-94,-95,-103,-89,-129,-15,-16,-139,-141,-97,-96,-169,-170,-343,-149,-150,210,-76,-345,-181,-55,-333,-30,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,210,210,210,-152,-159,-344,-345,-162,-163,-145,-147,-13,-345,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,361,-14,-345,374,375,377,-243,-247,-282,-77,-38,-136,-138,-196,-73,-334,-69,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-35,-36,-140,-142,-171,210,-154,210,-156,-151,-160,467,-143,-144,-148,-25,-26,-164,-166,-146,-130,-177,-178,-223,-222,-13,-227,-229,-242,-345,-87,-74,-345,485,-238,-239,486,-241,-43,-44,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-300,-301,-302,-303,-304,-31,-34,-172,-173,-153,-155,-161,-168,-224,-228,-226,-245,-244,-86,-75,533,-345,-237,-240,-248,-197,-39,-42,-283,-68,-298,-299,-289,-290,-32,-33,-165,-167,-225,-345,-345,-345,-345,565,-198,-40,-41,-262,-230,-87,-74,-232,-233,580,-307,-314,-345,588,-308,-231,-234,-345,-345,-236,-235,]),'PPHASH':([0,2,4,5,6,7,8,9,10,14,15,64,90,91,127,208,251,262,267,355,501,],[14,14,-60,-62,-63,-64,-65,-66,-67,-70,-71,-61,-90,-72,-76,-344,-77,-73,-69,-223,-68,]),'PPPRAGMA':([0,2,4,5,6,7,8,9,10,14,15,64,90,91,121,124,127,128,203,204,205,207,208,210,211,221,222,223,224,225,226,227,228,229,230,231,232,242,251,262,267,333,335,338,355,356,358,360,361,369,370,371,374,375,377,467,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[15,15,-60,-62,-63,-64,-65,-66,-67,-70,-71,-61,-90,-72,-343,15,-76,15,15,15,15,-159,-344,-162,-163,15,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,15,-77,-73,-69,15,15,-160,-223,-222,15,15,-242,15,-87,-74,-238,-239,-241,-161,-224,15,-226,-86,-75,-237,-240,-68,-225,15,15,15,-230,-87,-74,-232,-233,15,-231,-234,15,15,-236,-235,]),'_PRAGMA':([0,2,4,5,6,7,8,9,10,14,15,64,90,91,121,124,127,128,203,204,205,207,208,210,211,221,222,223,224,225,226,227,228,229,230,231,232,242,251,262,267,333,335,338,355,356,358,360,361,369,370,371,374,375,377,467,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[16,16,-60,-62,-63,-64,-65,-66,-67,-70,-71,-61,-90,-72,-343,16,-76,16,16,16,16,-159,-344,-162,-163,16,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,16,-77,-73,-69,16,16,-160,-223,-222,16,16,-242,16,-87,-74,-238,-239,-241,-161,-224,16,-226,-86,-75,-237,-240,-68,-225,16,16,16,-230,-87,-74,-232,-233,16,-231,-234,16,16,-236,-235,]),'_STATIC_ASSERT':([0,2,4,5,6,7,8,9,10,14,15,64,90,91,121,127,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,251,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[18,18,-60,-62,-63,-64,-65,-66,-67,-70,-71,-61,-90,-72,-343,-76,18,-344,18,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,18,-77,-73,-69,-223,-222,18,18,-242,18,-87,-74,-238,-239,-241,-224,18,-226,-86,-75,-237,-240,-68,-225,18,18,18,-230,-87,-74,-232,-233,18,-231,-234,18,18,-236,-235,]),'ID':([0,2,4,5,6,7,8,9,10,12,14,15,17,20,21,22,23,24,25,26,27,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,62,63,64,69,70,71,72,74,75,76,77,78,80,81,82,90,91,94,95,96,98,99,100,101,102,103,104,106,112,113,114,115,116,117,118,119,120,121,122,123,126,127,128,134,135,136,137,141,147,148,149,150,153,154,155,156,160,161,182,183,184,192,194,195,196,197,198,199,206,208,209,212,214,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,244,247,251,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,294,298,306,309,310,314,318,322,323,330,331,332,334,336,337,340,341,342,347,348,349,353,354,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,398,400,401,402,405,448,449,452,455,457,458,459,461,462,465,466,468,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,509,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,570,571,578,580,587,588,590,592,593,594,],[28,28,-60,-62,-63,-64,-65,-66,-67,28,-70,-71,28,28,-345,-345,-345,-128,-102,28,-345,-107,-345,-125,-126,-127,-129,-246,118,122,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-157,-158,-61,28,28,-129,-134,-98,-99,-100,-101,-104,28,-134,28,-90,-72,159,-345,159,-93,-9,-10,-345,-94,-95,-103,-129,-97,-183,-27,-28,-185,-96,-169,-170,202,-343,-149,-150,159,-76,233,28,159,-345,159,159,-292,-293,-294,-291,159,159,159,159,-295,-296,159,-345,-28,28,28,159,-184,-186,202,202,-152,-344,28,-145,-147,233,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,159,159,233,373,159,-77,-345,159,-345,-28,-73,-69,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,159,431,433,159,159,-292,159,159,159,28,28,-345,-171,202,159,-154,-156,-151,-143,-144,-148,159,-146,-130,-177,-178,-223,-222,233,233,-242,159,159,159,159,233,-87,-74,159,-238,-239,-241,159,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,159,-12,159,159,-292,159,159,159,-345,159,28,159,-345,-28,159,-172,-173,-153,-155,28,159,-224,233,-226,159,-86,-75,159,-237,-240,-345,-201,-345,-68,159,159,159,159,-345,-28,159,159,-292,-225,233,233,233,159,159,159,-11,-292,159,159,-230,-87,-74,-232,-233,159,-345,159,159,233,159,-231,-234,233,233,-236,-235,]),'LPAREN':([0,2,4,5,6,7,8,9,10,12,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,64,69,70,71,72,74,75,76,77,78,80,81,82,88,89,90,91,94,95,97,98,99,100,101,102,103,104,106,109,112,113,114,115,116,117,118,119,121,122,123,126,127,128,132,134,135,136,139,140,141,143,147,148,149,150,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,192,194,195,196,197,206,208,209,212,214,216,221,222,223,224,225,226,227,228,229,230,231,232,233,234,237,238,240,241,242,243,247,251,252,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,294,298,300,301,302,303,306,309,310,311,312,318,319,322,323,324,325,330,332,334,336,337,340,341,342,347,348,349,351,352,353,354,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,403,404,405,406,429,431,432,433,434,439,440,446,447,448,452,455,457,458,459,461,462,465,466,468,469,471,472,473,476,480,481,482,484,485,486,489,491,495,496,500,501,502,503,504,505,510,511,512,513,514,517,518,520,521,522,524,528,529,530,531,532,533,536,537,539,540,547,548,549,550,551,552,555,556,557,558,559,560,561,562,565,567,568,569,571,572,573,576,577,578,580,582,585,586,587,588,590,592,593,594,],[17,17,-60,-62,-63,-64,-65,-66,-67,82,-70,-71,92,17,94,96,17,-345,-345,-345,-128,-102,17,-345,-29,-107,-345,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,125,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,126,-61,82,17,-129,125,-98,-99,-100,-101,-104,82,-134,82,137,-37,-90,-72,141,-345,96,-93,-9,-10,-345,-94,-95,-103,-129,125,-97,-183,-27,-28,-185,-96,-169,-170,-343,-149,-150,141,-76,238,137,82,238,-345,-333,-30,238,-311,-292,-293,-294,-291,288,294,294,141,298,299,-297,-320,-295,-296,-309,-310,-312,304,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,238,-345,-28,322,82,238,-184,-186,-152,-344,82,-145,-147,351,238,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,141,362,238,366,367,238,372,238,-77,-38,-345,238,-345,-28,-73,-334,-69,238,141,141,141,141,141,141,141,141,141,141,141,141,141,141,141,141,141,141,238,238,-305,-306,238,238,-339,-340,-341,-342,-292,238,238,-35,-36,322,449,322,-345,-45,460,-171,141,-154,-156,-151,-143,-144,-148,141,-146,-130,351,351,-177,-178,-223,-222,238,238,-242,238,238,238,238,238,-87,-74,238,-238,-239,-241,238,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,238,-12,141,-292,238,238,-43,-44,141,-313,-300,-301,-302,-303,-304,-31,-34,449,460,-345,322,238,-345,-28,238,-172,-173,-153,-155,82,141,-224,238,-226,141,532,-86,-75,238,-237,-240,-345,-201,-39,-42,-345,-68,141,-298,-299,238,-32,-33,238,-345,-28,-210,-216,-214,238,238,-292,-225,238,238,238,238,238,238,-11,-40,-41,-292,238,238,-50,-51,-212,-211,-213,-215,-230,-87,-74,-232,-233,238,-307,-345,-314,238,-46,-49,-217,-218,238,238,-308,-47,-48,-231,-234,238,238,-236,-235,]),'TIMES':([0,2,4,5,6,7,8,9,10,12,14,15,17,21,22,23,24,25,26,27,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,69,70,71,72,74,75,76,77,78,81,82,90,91,94,95,98,99,100,101,102,103,104,106,112,113,114,115,116,117,118,119,121,122,123,126,127,128,134,135,136,139,141,143,145,146,147,148,149,150,151,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,192,194,195,197,206,208,209,212,214,216,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,250,251,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,300,301,302,303,306,309,310,322,323,330,332,334,336,337,340,341,342,347,348,349,351,353,354,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,448,455,457,458,459,461,462,465,466,468,469,471,472,473,476,481,482,484,485,486,489,491,499,500,501,502,503,504,505,507,508,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[30,30,-60,-62,-63,-64,-65,-66,-67,30,-70,-71,30,-345,-345,-345,-128,-102,30,-345,-107,-345,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,30,30,-129,-134,-98,-99,-100,-101,-104,-134,30,-90,-72,147,-345,-93,-9,-10,-345,-94,-95,-103,-129,-97,30,-27,-28,-185,-96,-169,-170,-343,-149,-150,147,-76,147,30,147,-345,-333,147,-311,269,-263,-292,-293,-294,-291,-282,-284,147,147,147,147,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,306,-345,-28,30,30,147,-186,-152,-344,30,-145,-147,30,147,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,147,147,147,147,-282,-77,-345,400,-345,-28,-73,-334,-69,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,147,-305,-306,-285,147,-286,-287,-288,147,-339,-340,-341,-342,-292,147,147,30,456,-171,147,-154,-156,-151,-143,-144,-148,147,-146,-130,30,-177,-178,-223,-222,147,147,-242,147,147,147,147,147,-87,-74,147,-238,-239,-241,147,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,147,-12,147,-292,147,147,147,-313,-264,-265,-266,269,269,269,269,269,269,269,269,269,269,269,269,269,269,269,-300,-301,-302,-303,-304,-345,147,-345,-28,524,-172,-173,-153,-155,30,147,-224,147,-226,147,-86,-75,147,-237,-240,-345,-201,-283,-345,-68,147,-298,-299,147,-289,-290,547,-345,-28,147,147,-292,-225,147,147,147,147,147,147,-11,-292,147,147,-230,-87,-74,-232,-233,147,-307,-345,-314,147,147,147,-308,-231,-234,147,147,-236,-235,]),'TYPEID':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,62,63,64,67,68,69,70,71,72,73,74,75,76,77,78,80,81,82,90,91,96,97,98,99,100,101,102,103,104,106,112,113,114,115,116,117,118,119,121,122,123,124,125,126,127,128,129,134,137,140,141,192,193,194,196,197,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,289,290,294,298,299,304,311,312,313,318,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,468,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[35,35,-60,-62,-63,-64,-65,-66,-67,35,89,-70,-71,-52,-345,-345,-345,-128,-102,35,-345,-29,-107,-345,-125,-126,-127,-129,-246,119,123,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-157,-158,-61,35,-91,89,35,-129,-134,35,-98,-99,-100,-101,-104,89,-134,89,-90,-72,35,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-183,-27,-28,-185,-96,-169,-170,-343,-149,-150,35,35,35,-76,35,-92,89,35,-30,35,324,35,89,-184,-186,35,35,35,-152,-159,-344,89,-162,-163,-145,35,-147,35,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,35,-77,-73,-69,432,434,35,35,35,35,-35,-36,35,324,35,-171,35,-154,35,-156,-151,-160,-143,-144,-148,-146,-130,35,-177,-178,-223,-222,-227,-229,-242,-87,-84,35,-238,-239,-241,-31,-34,35,35,-172,-173,-153,-155,-161,89,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'ENUM':([0,2,4,5,6,7,8,9,10,11,14,15,19,21,22,23,26,27,28,29,34,50,51,52,53,54,55,56,57,58,59,60,64,67,68,70,71,72,73,90,91,96,97,98,99,100,101,102,103,112,116,117,121,124,125,126,127,128,129,137,140,141,193,197,203,204,205,207,208,210,211,213,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,333,335,338,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[36,36,-60,-62,-63,-64,-65,-66,-67,36,-70,-71,-52,-345,-345,-345,36,-345,-29,-107,-345,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,36,-91,36,-345,-134,36,-90,-72,36,-53,-93,-9,-10,-345,-94,-95,-97,-185,-96,-343,36,36,36,-76,36,-92,36,-30,36,36,-186,36,36,36,-159,-344,-162,-163,36,36,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,36,-77,-73,-69,36,36,36,36,-35,-36,36,36,36,36,-160,-130,36,-177,-178,-223,-222,-227,-229,-242,-87,-84,36,-238,-239,-241,-31,-34,36,36,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'VOID':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[38,38,-60,-62,-63,-64,-65,-66,-67,38,38,-70,-71,-52,-345,-345,-345,-128,-102,38,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,38,-91,38,38,-129,-134,38,-98,-99,-100,-101,-104,-134,-90,-72,38,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,38,38,38,-76,38,-92,38,-30,38,38,38,-186,38,38,38,-152,-159,-344,38,-162,-163,-145,38,-147,38,38,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,38,-77,-73,-69,38,38,38,38,-35,-36,38,38,-171,38,-154,38,-156,-151,-160,-143,-144,-148,-146,-130,38,-177,-178,-223,-222,-227,-229,-242,-87,-84,38,-238,-239,-241,-31,-34,38,38,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_BOOL':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[39,39,-60,-62,-63,-64,-65,-66,-67,39,39,-70,-71,-52,-345,-345,-345,-128,-102,39,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,39,-91,39,39,-129,-134,39,-98,-99,-100,-101,-104,-134,-90,-72,39,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,39,39,39,-76,39,-92,39,-30,39,39,39,-186,39,39,39,-152,-159,-344,39,-162,-163,-145,39,-147,39,39,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,39,-77,-73,-69,39,39,39,39,-35,-36,39,39,-171,39,-154,39,-156,-151,-160,-143,-144,-148,-146,-130,39,-177,-178,-223,-222,-227,-229,-242,-87,-84,39,-238,-239,-241,-31,-34,39,39,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'CHAR':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[40,40,-60,-62,-63,-64,-65,-66,-67,40,40,-70,-71,-52,-345,-345,-345,-128,-102,40,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,40,-91,40,40,-129,-134,40,-98,-99,-100,-101,-104,-134,-90,-72,40,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,40,40,40,-76,40,-92,40,-30,40,40,40,-186,40,40,40,-152,-159,-344,40,-162,-163,-145,40,-147,40,40,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,40,-77,-73,-69,40,40,40,40,-35,-36,40,40,-171,40,-154,40,-156,-151,-160,-143,-144,-148,-146,-130,40,-177,-178,-223,-222,-227,-229,-242,-87,-84,40,-238,-239,-241,-31,-34,40,40,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'SHORT':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[41,41,-60,-62,-63,-64,-65,-66,-67,41,41,-70,-71,-52,-345,-345,-345,-128,-102,41,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,41,-91,41,41,-129,-134,41,-98,-99,-100,-101,-104,-134,-90,-72,41,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,41,41,41,-76,41,-92,41,-30,41,41,41,-186,41,41,41,-152,-159,-344,41,-162,-163,-145,41,-147,41,41,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,41,-77,-73,-69,41,41,41,41,-35,-36,41,41,-171,41,-154,41,-156,-151,-160,-143,-144,-148,-146,-130,41,-177,-178,-223,-222,-227,-229,-242,-87,-84,41,-238,-239,-241,-31,-34,41,41,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'INT':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[42,42,-60,-62,-63,-64,-65,-66,-67,42,42,-70,-71,-52,-345,-345,-345,-128,-102,42,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,42,-91,42,42,-129,-134,42,-98,-99,-100,-101,-104,-134,-90,-72,42,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,42,42,42,-76,42,-92,42,-30,42,42,42,-186,42,42,42,-152,-159,-344,42,-162,-163,-145,42,-147,42,42,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,42,-77,-73,-69,42,42,42,42,-35,-36,42,42,-171,42,-154,42,-156,-151,-160,-143,-144,-148,-146,-130,42,-177,-178,-223,-222,-227,-229,-242,-87,-84,42,-238,-239,-241,-31,-34,42,42,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'LONG':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[43,43,-60,-62,-63,-64,-65,-66,-67,43,43,-70,-71,-52,-345,-345,-345,-128,-102,43,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,43,-91,43,43,-129,-134,43,-98,-99,-100,-101,-104,-134,-90,-72,43,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,43,43,43,-76,43,-92,43,-30,43,43,43,-186,43,43,43,-152,-159,-344,43,-162,-163,-145,43,-147,43,43,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,43,-77,-73,-69,43,43,43,43,-35,-36,43,43,-171,43,-154,43,-156,-151,-160,-143,-144,-148,-146,-130,43,-177,-178,-223,-222,-227,-229,-242,-87,-84,43,-238,-239,-241,-31,-34,43,43,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'FLOAT':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[44,44,-60,-62,-63,-64,-65,-66,-67,44,44,-70,-71,-52,-345,-345,-345,-128,-102,44,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,44,-91,44,44,-129,-134,44,-98,-99,-100,-101,-104,-134,-90,-72,44,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,44,44,44,-76,44,-92,44,-30,44,44,44,-186,44,44,44,-152,-159,-344,44,-162,-163,-145,44,-147,44,44,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,44,-77,-73,-69,44,44,44,44,-35,-36,44,44,-171,44,-154,44,-156,-151,-160,-143,-144,-148,-146,-130,44,-177,-178,-223,-222,-227,-229,-242,-87,-84,44,-238,-239,-241,-31,-34,44,44,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'DOUBLE':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[45,45,-60,-62,-63,-64,-65,-66,-67,45,45,-70,-71,-52,-345,-345,-345,-128,-102,45,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,45,-91,45,45,-129,-134,45,-98,-99,-100,-101,-104,-134,-90,-72,45,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,45,45,45,-76,45,-92,45,-30,45,45,45,-186,45,45,45,-152,-159,-344,45,-162,-163,-145,45,-147,45,45,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,45,-77,-73,-69,45,45,45,45,-35,-36,45,45,-171,45,-154,45,-156,-151,-160,-143,-144,-148,-146,-130,45,-177,-178,-223,-222,-227,-229,-242,-87,-84,45,-238,-239,-241,-31,-34,45,45,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_COMPLEX':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[46,46,-60,-62,-63,-64,-65,-66,-67,46,46,-70,-71,-52,-345,-345,-345,-128,-102,46,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,46,-91,46,46,-129,-134,46,-98,-99,-100,-101,-104,-134,-90,-72,46,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,46,46,46,-76,46,-92,46,-30,46,46,46,-186,46,46,46,-152,-159,-344,46,-162,-163,-145,46,-147,46,46,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,46,-77,-73,-69,46,46,46,46,-35,-36,46,46,-171,46,-154,46,-156,-151,-160,-143,-144,-148,-146,-130,46,-177,-178,-223,-222,-227,-229,-242,-87,-84,46,-238,-239,-241,-31,-34,46,46,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'SIGNED':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[47,47,-60,-62,-63,-64,-65,-66,-67,47,47,-70,-71,-52,-345,-345,-345,-128,-102,47,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,47,-91,47,47,-129,-134,47,-98,-99,-100,-101,-104,-134,-90,-72,47,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,47,47,47,-76,47,-92,47,-30,47,47,47,-186,47,47,47,-152,-159,-344,47,-162,-163,-145,47,-147,47,47,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,47,-77,-73,-69,47,47,47,47,-35,-36,47,47,-171,47,-154,47,-156,-151,-160,-143,-144,-148,-146,-130,47,-177,-178,-223,-222,-227,-229,-242,-87,-84,47,-238,-239,-241,-31,-34,47,47,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'UNSIGNED':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[48,48,-60,-62,-63,-64,-65,-66,-67,48,48,-70,-71,-52,-345,-345,-345,-128,-102,48,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,48,-91,48,48,-129,-134,48,-98,-99,-100,-101,-104,-134,-90,-72,48,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,48,48,48,-76,48,-92,48,-30,48,48,48,-186,48,48,48,-152,-159,-344,48,-162,-163,-145,48,-147,48,48,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,48,-77,-73,-69,48,48,48,48,-35,-36,48,48,-171,48,-154,48,-156,-151,-160,-143,-144,-148,-146,-130,48,-177,-178,-223,-222,-227,-229,-242,-87,-84,48,-238,-239,-241,-31,-34,48,48,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'__INT128':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,96,97,98,99,100,101,102,103,104,106,112,116,117,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[49,49,-60,-62,-63,-64,-65,-66,-67,49,49,-70,-71,-52,-345,-345,-345,-128,-102,49,-345,-29,-107,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,49,-91,49,49,-129,-134,49,-98,-99,-100,-101,-104,-134,-90,-72,49,-53,-93,-9,-10,-345,-94,-95,-103,-129,-97,-185,-96,-169,-170,-343,-149,-150,49,49,49,-76,49,-92,49,-30,49,49,49,-186,49,49,49,-152,-159,-344,49,-162,-163,-145,49,-147,49,49,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,49,-77,-73,-69,49,49,49,49,-35,-36,49,49,-171,49,-154,49,-156,-151,-160,-143,-144,-148,-146,-130,49,-177,-178,-223,-222,-227,-229,-242,-87,-84,49,-238,-239,-241,-31,-34,49,49,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_ATOMIC':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,70,71,72,73,74,75,76,77,78,81,90,91,95,96,97,98,99,100,101,102,103,104,106,112,115,116,117,118,119,121,122,123,124,125,126,127,128,129,136,137,140,141,183,184,192,193,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,258,259,262,267,294,298,299,304,311,312,313,322,323,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,448,449,457,458,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,513,514,528,558,559,560,561,562,587,588,593,594,],[50,50,-60,-62,-63,-64,-65,-66,-67,72,81,-70,-71,-52,72,72,72,-128,-102,109,72,-29,-107,81,-125,-126,-127,72,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,72,-91,81,109,72,-134,72,-98,-99,-100,-101,-104,-134,-90,-72,81,50,-53,-93,-9,-10,72,-94,-95,-103,-129,-97,81,-185,-96,-169,-170,-343,-149,-150,50,50,50,-76,72,-92,81,50,-30,50,81,81,81,109,-186,50,50,50,-152,-159,-344,81,-162,-163,-145,72,-147,81,72,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,50,-77,81,81,-73,-69,50,50,50,50,-35,-36,50,50,81,-171,50,-154,50,-156,-151,-160,-143,-144,-148,-146,-130,50,-177,-178,-223,-222,-227,-229,-242,-87,-84,72,-238,-239,-241,-31,-34,81,50,81,81,50,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,81,81,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'CONST':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,95,96,97,101,104,106,115,116,118,119,121,122,123,124,125,126,127,128,129,136,137,140,141,183,184,192,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,258,259,262,267,294,298,299,304,311,312,313,322,323,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,448,449,457,458,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,513,514,528,558,559,560,561,562,587,588,593,594,],[51,51,-60,-62,-63,-64,-65,-66,-67,51,51,-70,-71,-52,51,51,51,-128,-102,51,-29,-107,51,-125,-126,-127,51,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,51,-91,51,51,-134,51,-98,-99,-100,-101,-104,-134,-90,-72,51,51,-53,51,-103,-129,51,-185,-169,-170,-343,-149,-150,51,51,51,-76,51,-92,51,51,-30,51,51,51,51,-186,51,51,51,-152,-159,-344,51,-162,-163,-145,51,-147,51,51,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,51,-77,51,51,-73,-69,51,51,51,51,-35,-36,51,51,51,-171,51,-154,51,-156,-151,-160,-143,-144,-148,-146,-130,51,-177,-178,-223,-222,-227,-229,-242,-87,-84,51,-238,-239,-241,-31,-34,51,51,51,51,51,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,51,51,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'RESTRICT':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,95,96,97,101,104,106,115,116,118,119,121,122,123,124,125,126,127,128,129,136,137,140,141,183,184,192,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,258,259,262,267,294,298,299,304,311,312,313,322,323,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,448,449,457,458,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,513,514,528,558,559,560,561,562,587,588,593,594,],[52,52,-60,-62,-63,-64,-65,-66,-67,52,52,-70,-71,-52,52,52,52,-128,-102,52,-29,-107,52,-125,-126,-127,52,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,52,-91,52,52,-134,52,-98,-99,-100,-101,-104,-134,-90,-72,52,52,-53,52,-103,-129,52,-185,-169,-170,-343,-149,-150,52,52,52,-76,52,-92,52,52,-30,52,52,52,52,-186,52,52,52,-152,-159,-344,52,-162,-163,-145,52,-147,52,52,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,52,-77,52,52,-73,-69,52,52,52,52,-35,-36,52,52,52,-171,52,-154,52,-156,-151,-160,-143,-144,-148,-146,-130,52,-177,-178,-223,-222,-227,-229,-242,-87,-84,52,-238,-239,-241,-31,-34,52,52,52,52,52,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,52,52,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'VOLATILE':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,95,96,97,101,104,106,115,116,118,119,121,122,123,124,125,126,127,128,129,136,137,140,141,183,184,192,197,203,204,205,206,207,208,209,210,211,212,213,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,258,259,262,267,294,298,299,304,311,312,313,322,323,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,448,449,457,458,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,513,514,528,558,559,560,561,562,587,588,593,594,],[53,53,-60,-62,-63,-64,-65,-66,-67,53,53,-70,-71,-52,53,53,53,-128,-102,53,-29,-107,53,-125,-126,-127,53,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,53,-91,53,53,-134,53,-98,-99,-100,-101,-104,-134,-90,-72,53,53,-53,53,-103,-129,53,-185,-169,-170,-343,-149,-150,53,53,53,-76,53,-92,53,53,-30,53,53,53,53,-186,53,53,53,-152,-159,-344,53,-162,-163,-145,53,-147,53,53,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,53,-77,53,53,-73,-69,53,53,53,53,-35,-36,53,53,53,-171,53,-154,53,-156,-151,-160,-143,-144,-148,-146,-130,53,-177,-178,-223,-222,-227,-229,-242,-87,-84,53,-238,-239,-241,-31,-34,53,53,53,53,53,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,53,53,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'AUTO':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[54,54,-60,-62,-63,-64,-65,-66,-67,54,54,-70,-71,-52,54,54,54,-128,-102,54,-29,-107,-125,-126,-127,54,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,54,-91,54,54,-134,54,-98,-99,-100,-101,-104,-134,-90,-72,54,-53,54,-103,-129,-169,-170,-343,-149,-150,-76,54,-92,54,-30,54,-152,-344,54,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,54,54,-171,-154,-156,-151,-130,54,-177,-178,-223,-222,-227,-229,-242,-87,-84,54,-238,-239,-241,-31,-34,54,54,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'REGISTER':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[55,55,-60,-62,-63,-64,-65,-66,-67,55,55,-70,-71,-52,55,55,55,-128,-102,55,-29,-107,-125,-126,-127,55,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,55,-91,55,55,-134,55,-98,-99,-100,-101,-104,-134,-90,-72,55,-53,55,-103,-129,-169,-170,-343,-149,-150,-76,55,-92,55,-30,55,-152,-344,55,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,55,55,-171,-154,-156,-151,-130,55,-177,-178,-223,-222,-227,-229,-242,-87,-84,55,-238,-239,-241,-31,-34,55,55,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'STATIC':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,95,96,97,101,104,106,116,118,119,121,122,123,127,128,129,136,137,140,184,192,197,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,259,262,267,311,312,313,322,323,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,448,449,458,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,514,528,558,559,560,561,562,587,588,593,594,],[29,29,-60,-62,-63,-64,-65,-66,-67,29,29,-70,-71,-52,29,29,29,-128,-102,29,-29,-107,-125,-126,-127,29,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,29,-91,29,29,-134,29,-98,-99,-100,-101,-104,-134,-90,-72,183,29,-53,29,-103,-129,-185,-169,-170,-343,-149,-150,-76,29,-92,258,29,-30,310,29,-186,-152,-344,29,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,402,-73,-69,-35,-36,29,29,457,-171,-154,-156,-151,-130,29,-177,-178,-223,-222,-227,-229,-242,-87,-84,29,-238,-239,-241,-31,-34,513,29,522,29,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,549,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'EXTERN':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[56,56,-60,-62,-63,-64,-65,-66,-67,56,56,-70,-71,-52,56,56,56,-128,-102,56,-29,-107,-125,-126,-127,56,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,56,-91,56,56,-134,56,-98,-99,-100,-101,-104,-134,-90,-72,56,-53,56,-103,-129,-169,-170,-343,-149,-150,-76,56,-92,56,-30,56,-152,-344,56,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,56,56,-171,-154,-156,-151,-130,56,-177,-178,-223,-222,-227,-229,-242,-87,-84,56,-238,-239,-241,-31,-34,56,56,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'TYPEDEF':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[57,57,-60,-62,-63,-64,-65,-66,-67,57,57,-70,-71,-52,57,57,57,-128,-102,57,-29,-107,-125,-126,-127,57,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,57,-91,57,57,-134,57,-98,-99,-100,-101,-104,-134,-90,-72,57,-53,57,-103,-129,-169,-170,-343,-149,-150,-76,57,-92,57,-30,57,-152,-344,57,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,57,57,-171,-154,-156,-151,-130,57,-177,-178,-223,-222,-227,-229,-242,-87,-84,57,-238,-239,-241,-31,-34,57,57,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_THREAD_LOCAL':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[58,58,-60,-62,-63,-64,-65,-66,-67,58,58,-70,-71,-52,58,58,58,-128,-102,58,-29,-107,-125,-126,-127,58,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,58,-91,58,58,-134,58,-98,-99,-100,-101,-104,-134,-90,-72,58,-53,58,-103,-129,-169,-170,-343,-149,-150,-76,58,-92,58,-30,58,-152,-344,58,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,58,58,-171,-154,-156,-151,-130,58,-177,-178,-223,-222,-227,-229,-242,-87,-84,58,-238,-239,-241,-31,-34,58,58,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'INLINE':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[59,59,-60,-62,-63,-64,-65,-66,-67,59,59,-70,-71,-52,59,59,59,-128,-102,59,-29,-107,-125,-126,-127,59,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,59,-91,59,59,-134,59,-98,-99,-100,-101,-104,-134,-90,-72,59,-53,59,-103,-129,-169,-170,-343,-149,-150,-76,59,-92,59,-30,59,-152,-344,59,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,59,59,-171,-154,-156,-151,-130,59,-177,-178,-223,-222,-227,-229,-242,-87,-84,59,-238,-239,-241,-31,-34,59,59,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_NORETURN':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,127,128,129,137,140,192,206,208,221,222,223,224,225,226,227,228,229,230,231,232,251,262,267,311,312,313,322,330,334,336,337,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[60,60,-60,-62,-63,-64,-65,-66,-67,60,60,-70,-71,-52,60,60,60,-128,-102,60,-29,-107,-125,-126,-127,60,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,60,-91,60,60,-134,60,-98,-99,-100,-101,-104,-134,-90,-72,60,-53,60,-103,-129,-169,-170,-343,-149,-150,-76,60,-92,60,-30,60,-152,-344,60,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-77,-73,-69,-35,-36,60,60,-171,-154,-156,-151,-130,60,-177,-178,-223,-222,-227,-229,-242,-87,-84,60,-238,-239,-241,-31,-34,60,60,-172,-173,-153,-155,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'_ALIGNAS':([0,2,4,5,6,7,8,9,10,11,12,14,15,19,21,22,23,24,25,27,28,29,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,64,67,68,69,71,72,73,74,75,76,77,78,81,90,91,96,97,101,104,106,118,119,121,122,123,124,125,126,127,128,129,137,140,141,192,203,204,205,206,207,208,209,210,211,212,214,216,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,330,333,334,335,336,337,338,340,341,342,348,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,461,462,465,466,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[61,61,-60,-62,-63,-64,-65,-66,-67,61,61,-70,-71,-52,61,61,61,-128,-102,61,-29,-107,-125,-126,-127,61,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,61,-91,61,61,-134,61,-98,-99,-100,-101,-104,-134,-90,-72,61,-53,61,-103,-129,-169,-170,-343,-149,-150,61,61,61,-76,61,-92,61,-30,61,61,61,61,61,-152,-159,-344,61,-162,-163,-145,-147,61,61,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,61,-77,-73,-69,61,61,61,61,-35,-36,61,61,-171,61,-154,61,-156,-151,-160,-143,-144,-148,-146,-130,61,-177,-178,-223,-222,-227,-229,-242,-87,-84,61,-238,-239,-241,-31,-34,61,61,-172,-173,-153,-155,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'STRUCT':([0,2,4,5,6,7,8,9,10,11,14,15,19,21,22,23,26,27,28,29,34,50,51,52,53,54,55,56,57,58,59,60,64,67,68,70,71,72,73,90,91,96,97,98,99,100,101,102,103,112,116,117,121,124,125,126,127,128,129,137,140,141,193,197,203,204,205,207,208,210,211,213,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,333,335,338,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[62,62,-60,-62,-63,-64,-65,-66,-67,62,-70,-71,-52,-345,-345,-345,62,-345,-29,-107,-345,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,62,-91,62,-345,-134,62,-90,-72,62,-53,-93,-9,-10,-345,-94,-95,-97,-185,-96,-343,62,62,62,-76,62,-92,62,-30,62,62,-186,62,62,62,-159,-344,-162,-163,62,62,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,62,-77,-73,-69,62,62,62,62,-35,-36,62,62,62,62,-160,-130,62,-177,-178,-223,-222,-227,-229,-242,-87,-84,62,-238,-239,-241,-31,-34,62,62,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'UNION':([0,2,4,5,6,7,8,9,10,11,14,15,19,21,22,23,26,27,28,29,34,50,51,52,53,54,55,56,57,58,59,60,64,67,68,70,71,72,73,90,91,96,97,98,99,100,101,102,103,112,116,117,121,124,125,126,127,128,129,137,140,141,193,197,203,204,205,207,208,210,211,213,221,222,223,224,225,226,227,228,229,230,231,232,238,251,262,267,294,298,299,304,311,312,313,322,333,335,338,349,351,353,354,355,356,358,360,361,370,371,372,374,375,377,439,440,449,460,467,471,472,473,481,482,485,486,501,510,511,528,558,559,560,561,562,587,588,593,594,],[63,63,-60,-62,-63,-64,-65,-66,-67,63,-70,-71,-52,-345,-345,-345,63,-345,-29,-107,-345,-134,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-61,63,-91,63,-345,-134,63,-90,-72,63,-53,-93,-9,-10,-345,-94,-95,-97,-185,-96,-343,63,63,63,-76,63,-92,63,-30,63,63,-186,63,63,63,-159,-344,-162,-163,63,63,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,63,-77,-73,-69,63,63,63,63,-35,-36,63,63,63,63,-160,-130,63,-177,-178,-223,-222,-227,-229,-242,-87,-84,63,-238,-239,-241,-31,-34,63,63,-161,-224,-228,-226,-86,-84,-237,-240,-68,-32,-33,-225,-230,-87,-84,-232,-233,-231,-234,-236,-235,]),'LBRACE':([11,15,19,28,36,37,62,63,65,66,67,68,73,90,91,97,118,119,121,122,123,128,129,131,135,140,195,208,221,222,223,224,225,226,227,228,229,230,231,232,238,242,256,262,267,311,312,355,356,358,360,361,369,370,371,374,375,377,392,393,394,405,439,440,471,472,473,476,481,482,485,486,489,491,500,501,506,507,510,511,528,529,530,531,536,537,558,559,560,561,562,568,578,587,588,590,592,593,594,],[-345,-71,-52,-29,121,121,-157,-158,121,-7,-8,-91,-345,-90,-72,-53,121,121,-343,121,121,121,-92,121,121,-30,121,-344,121,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,121,121,-345,-73,-69,-35,-36,-223,-222,121,121,-242,121,-87,-74,-238,-239,-241,-11,121,-12,121,-31,-34,-224,121,-226,121,-86,-75,-237,-240,-345,-201,-345,-68,121,121,-32,-33,-225,121,121,121,121,-11,-230,-87,-74,-232,-233,-345,121,-231,-234,121,121,-236,-235,]),'RBRACE':([15,90,91,121,124,128,139,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,200,201,202,203,204,205,207,208,210,211,219,220,221,222,223,224,225,226,227,228,229,230,231,232,249,250,255,256,262,263,267,291,292,293,295,296,297,300,301,302,303,328,329,331,333,335,338,355,356,358,360,361,370,371,374,375,377,390,391,392,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,463,464,467,471,472,473,475,481,482,485,486,487,488,489,490,499,501,503,504,507,508,528,535,541,542,558,559,560,561,562,566,567,568,569,582,587,588,593,594,],[-71,-90,-72,-343,208,-345,-333,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,208,-174,-179,208,208,208,-159,-344,-162,-163,208,-5,-6,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-247,-282,-196,-345,-73,-334,-69,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,208,208,-175,208,208,-160,-223,-222,-227,-229,-242,-87,-84,-238,-239,-241,208,-22,-21,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-300,-301,-302,-303,-304,-176,-180,-161,-224,-228,-226,-245,-86,-84,-237,-240,-248,-197,208,-199,-283,-68,-298,-299,-289,-290,-225,-198,208,-262,-230,-87,-84,-232,-233,-200,-307,208,-314,-308,-231,-234,-236,-235,]),'CASE':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,234,-344,234,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,234,-73,-69,-223,-222,234,234,-242,234,-87,-74,-238,-239,-241,-224,234,-226,-86,-75,-237,-240,-68,-225,234,234,234,-230,-87,-74,-232,-233,234,-231,-234,234,234,-236,-235,]),'DEFAULT':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,235,-344,235,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,235,-73,-69,-223,-222,235,235,-242,235,-87,-74,-238,-239,-241,-224,235,-226,-86,-75,-237,-240,-68,-225,235,235,235,-230,-87,-74,-232,-233,235,-231,-234,235,235,-236,-235,]),'IF':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,237,-344,237,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,237,-73,-69,-223,-222,237,237,-242,237,-87,-74,-238,-239,-241,-224,237,-226,-86,-75,-237,-240,-68,-225,237,237,237,-230,-87,-74,-232,-233,237,-231,-234,237,237,-236,-235,]),'SWITCH':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,240,-344,240,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,240,-73,-69,-223,-222,240,240,-242,240,-87,-74,-238,-239,-241,-224,240,-226,-86,-75,-237,-240,-68,-225,240,240,240,-230,-87,-74,-232,-233,240,-231,-234,240,240,-236,-235,]),'WHILE':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,368,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,241,-344,241,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,241,-73,-69,-223,-222,241,241,-242,480,241,-87,-74,-238,-239,-241,-224,241,-226,-86,-75,-237,-240,-68,-225,241,241,241,-230,-87,-74,-232,-233,241,-231,-234,241,241,-236,-235,]),'DO':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,242,-344,242,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,242,-73,-69,-223,-222,242,242,-242,242,-87,-74,-238,-239,-241,-224,242,-226,-86,-75,-237,-240,-68,-225,242,242,242,-230,-87,-74,-232,-233,242,-231,-234,242,242,-236,-235,]),'FOR':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,243,-344,243,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,243,-73,-69,-223,-222,243,243,-242,243,-87,-74,-238,-239,-241,-224,243,-226,-86,-75,-237,-240,-68,-225,243,243,243,-230,-87,-74,-232,-233,243,-231,-234,243,243,-236,-235,]),'GOTO':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,244,-344,244,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,244,-73,-69,-223,-222,244,244,-242,244,-87,-74,-238,-239,-241,-224,244,-226,-86,-75,-237,-240,-68,-225,244,244,244,-230,-87,-74,-232,-233,244,-231,-234,244,244,-236,-235,]),'BREAK':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,245,-344,245,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,245,-73,-69,-223,-222,245,245,-242,245,-87,-74,-238,-239,-241,-224,245,-226,-86,-75,-237,-240,-68,-225,245,245,245,-230,-87,-74,-232,-233,245,-231,-234,245,245,-236,-235,]),'CONTINUE':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,246,-344,246,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,246,-73,-69,-223,-222,246,246,-242,246,-87,-74,-238,-239,-241,-224,246,-226,-86,-75,-237,-240,-68,-225,246,246,246,-230,-87,-74,-232,-233,246,-231,-234,246,246,-236,-235,]),'RETURN':([15,90,91,121,128,208,221,222,223,224,225,226,227,228,229,230,231,232,242,262,267,355,356,358,360,361,369,370,371,374,375,377,471,472,473,481,482,485,486,501,528,529,530,531,558,559,560,561,562,578,587,588,590,592,593,594,],[-71,-90,-72,-343,247,-344,247,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,247,-73,-69,-223,-222,247,247,-242,247,-87,-74,-238,-239,-241,-224,247,-226,-86,-75,-237,-240,-68,-225,247,247,247,-230,-87,-74,-232,-233,247,-231,-234,247,247,-236,-235,]),'PLUSPLUS':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,139,141,143,147,148,149,150,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,429,431,432,433,434,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,503,504,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,153,-345,-27,-28,-185,-343,153,153,153,-345,-333,153,-311,-292,-293,-294,-291,291,153,153,153,153,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,153,-345,-28,153,-186,-344,153,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,153,153,153,153,-345,153,-345,-28,-73,-334,-69,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,153,-305,-306,153,153,-339,-340,-341,-342,-292,153,153,-345,153,153,-223,-222,153,153,-242,153,153,153,153,153,-87,-74,153,-238,-239,-241,153,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,153,-12,153,-292,153,153,153,-313,-300,-301,-302,-303,-304,-345,153,-345,-28,153,153,-224,153,-226,153,-86,-75,153,-237,-240,-345,-201,-345,-68,153,-298,-299,153,153,-345,-28,153,153,-292,-225,153,153,153,153,153,153,-11,-292,153,153,-230,-87,-74,-232,-233,153,-307,-345,-314,153,153,153,-308,-231,-234,153,153,-236,-235,]),'MINUSMINUS':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,139,141,143,147,148,149,150,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,429,431,432,433,434,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,503,504,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,154,-345,-27,-28,-185,-343,154,154,154,-345,-333,154,-311,-292,-293,-294,-291,292,154,154,154,154,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,154,-345,-28,154,-186,-344,154,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,154,154,154,154,-345,154,-345,-28,-73,-334,-69,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,154,-305,-306,154,154,-339,-340,-341,-342,-292,154,154,-345,154,154,-223,-222,154,154,-242,154,154,154,154,154,-87,-74,154,-238,-239,-241,154,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,154,-12,154,-292,154,154,154,-313,-300,-301,-302,-303,-304,-345,154,-345,-28,154,154,-224,154,-226,154,-86,-75,154,-237,-240,-345,-201,-345,-68,154,-298,-299,154,154,-345,-28,154,154,-292,-225,154,154,154,154,154,154,-11,-292,154,154,-230,-87,-74,-232,-233,154,-307,-345,-314,154,154,154,-308,-231,-234,154,154,-236,-235,]),'SIZEOF':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,156,-345,-27,-28,-185,-343,156,156,156,-345,156,-292,-293,-294,-291,156,156,156,156,-295,-296,156,-345,-28,156,-186,-344,156,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,156,156,156,156,-345,156,-345,-28,-73,-69,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,156,-292,156,156,-345,156,156,-223,-222,156,156,-242,156,156,156,156,156,-87,-74,156,-238,-239,-241,156,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,156,-12,156,-292,156,156,156,-345,156,-345,-28,156,156,-224,156,-226,156,-86,-75,156,-237,-240,-345,-201,-345,-68,156,156,156,-345,-28,156,156,-292,-225,156,156,156,156,156,156,-11,-292,156,156,-230,-87,-74,-232,-233,156,-345,156,156,156,-231,-234,156,156,-236,-235,]),'_ALIGNOF':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,157,-345,-27,-28,-185,-343,157,157,157,-345,157,-292,-293,-294,-291,157,157,157,157,-295,-296,157,-345,-28,157,-186,-344,157,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,157,157,157,157,-345,157,-345,-28,-73,-69,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,157,-292,157,157,-345,157,157,-223,-222,157,157,-242,157,157,157,157,157,-87,-74,157,-238,-239,-241,157,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,157,-12,157,-292,157,157,157,-345,157,-345,-28,157,157,-224,157,-226,157,-86,-75,157,-237,-240,-345,-201,-345,-68,157,157,157,-345,-28,157,157,-292,-225,157,157,157,157,157,157,-11,-292,157,157,-230,-87,-74,-232,-233,157,-345,157,157,157,-231,-234,157,157,-236,-235,]),'AND':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,139,141,143,145,146,147,148,149,150,151,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,250,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,499,500,501,502,503,504,505,507,508,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,150,-345,-27,-28,-185,-343,150,150,150,-345,-333,150,-311,282,-263,-292,-293,-294,-291,-282,-284,150,150,150,150,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,150,-345,-28,150,-186,-344,150,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,150,150,150,150,-282,-345,150,-345,-28,-73,-334,-69,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,150,-305,-306,-285,150,-286,-287,-288,150,-339,-340,-341,-342,-292,150,150,-345,150,150,-223,-222,150,150,-242,150,150,150,150,150,-87,-74,150,-238,-239,-241,150,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,150,-12,150,-292,150,150,150,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,282,282,282,282,-300,-301,-302,-303,-304,-345,150,-345,-28,150,150,-224,150,-226,150,-86,-75,150,-237,-240,-345,-201,-283,-345,-68,150,-298,-299,150,-289,-290,150,-345,-28,150,150,-292,-225,150,150,150,150,150,150,-11,-292,150,150,-230,-87,-74,-232,-233,150,-307,-345,-314,150,150,150,-308,-231,-234,150,150,-236,-235,]),'PLUS':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,139,141,143,145,146,147,148,149,150,151,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,250,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,499,500,501,502,503,504,505,507,508,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,148,-345,-27,-28,-185,-343,148,148,148,-345,-333,148,-311,272,-263,-292,-293,-294,-291,-282,-284,148,148,148,148,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,148,-345,-28,148,-186,-344,148,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,148,148,148,148,-282,-345,148,-345,-28,-73,-334,-69,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,148,-305,-306,-285,148,-286,-287,-288,148,-339,-340,-341,-342,-292,148,148,-345,148,148,-223,-222,148,148,-242,148,148,148,148,148,-87,-74,148,-238,-239,-241,148,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,148,-12,148,-292,148,148,148,-313,-264,-265,-266,-267,-268,272,272,272,272,272,272,272,272,272,272,272,272,272,-300,-301,-302,-303,-304,-345,148,-345,-28,148,148,-224,148,-226,148,-86,-75,148,-237,-240,-345,-201,-283,-345,-68,148,-298,-299,148,-289,-290,148,-345,-28,148,148,-292,-225,148,148,148,148,148,148,-11,-292,148,148,-230,-87,-74,-232,-233,148,-307,-345,-314,148,148,148,-308,-231,-234,148,148,-236,-235,]),'MINUS':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,139,141,143,145,146,147,148,149,150,151,152,153,154,155,156,158,159,160,161,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,233,234,238,242,247,250,256,257,258,259,262,263,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,291,292,293,294,295,296,297,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,499,500,501,502,503,504,505,507,508,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,567,568,569,571,578,580,582,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,149,-345,-27,-28,-185,-343,149,149,149,-345,-333,149,-311,273,-263,-292,-293,-294,-291,-282,-284,149,149,149,149,-297,-320,-295,-296,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,149,-345,-28,149,-186,-344,149,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,-320,149,149,149,149,-282,-345,149,-345,-28,-73,-334,-69,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,149,-305,-306,-285,149,-286,-287,-288,149,-339,-340,-341,-342,-292,149,149,-345,149,149,-223,-222,149,149,-242,149,149,149,149,149,-87,-74,149,-238,-239,-241,149,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,149,-12,149,-292,149,149,149,-313,-264,-265,-266,-267,-268,273,273,273,273,273,273,273,273,273,273,273,273,273,-300,-301,-302,-303,-304,-345,149,-345,-28,149,149,-224,149,-226,149,-86,-75,149,-237,-240,-345,-201,-283,-345,-68,149,-298,-299,149,-289,-290,149,-345,-28,149,149,-292,-225,149,149,149,149,149,149,-11,-292,149,149,-230,-87,-74,-232,-233,149,-307,-345,-314,149,149,149,-308,-231,-234,149,149,-236,-235,]),'NOT':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,160,-345,-27,-28,-185,-343,160,160,160,-345,160,-292,-293,-294,-291,160,160,160,160,-295,-296,160,-345,-28,160,-186,-344,160,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,160,160,160,160,-345,160,-345,-28,-73,-69,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,-292,160,160,-345,160,160,-223,-222,160,160,-242,160,160,160,160,160,-87,-74,160,-238,-239,-241,160,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,160,-12,160,-292,160,160,160,-345,160,-345,-28,160,160,-224,160,-226,160,-86,-75,160,-237,-240,-345,-201,-345,-68,160,160,160,-345,-28,160,160,-292,-225,160,160,160,160,160,160,-11,-292,160,160,-230,-87,-74,-232,-233,160,-345,160,160,160,-231,-234,160,160,-236,-235,]),'LNOT':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,161,-345,-27,-28,-185,-343,161,161,161,-345,161,-292,-293,-294,-291,161,161,161,161,-295,-296,161,-345,-28,161,-186,-344,161,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,161,161,161,161,-345,161,-345,-28,-73,-69,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,161,-292,161,161,-345,161,161,-223,-222,161,161,-242,161,161,161,161,161,-87,-74,161,-238,-239,-241,161,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,161,-12,161,-292,161,161,161,-345,161,-345,-28,161,161,-224,161,-226,161,-86,-75,161,-237,-240,-345,-201,-345,-68,161,161,161,-345,-28,161,161,-292,-225,161,161,161,161,161,161,-11,-292,161,161,-230,-87,-74,-232,-233,161,-345,161,161,161,-231,-234,161,161,-236,-235,]),'OFFSETOF':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,165,-345,-27,-28,-185,-343,165,165,165,-345,165,-292,-293,-294,-291,165,165,165,165,-295,-296,165,-345,-28,165,-186,-344,165,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,165,165,165,165,-345,165,-345,-28,-73,-69,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,165,-292,165,165,-345,165,165,-223,-222,165,165,-242,165,165,165,165,165,-87,-74,165,-238,-239,-241,165,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,165,-12,165,-292,165,165,165,-345,165,-345,-28,165,165,-224,165,-226,165,-86,-75,165,-237,-240,-345,-201,-345,-68,165,165,165,-345,-28,165,165,-292,-225,165,165,165,165,165,165,-11,-292,165,165,-230,-87,-74,-232,-233,165,-345,165,165,165,-231,-234,165,165,-236,-235,]),'INT_CONST_DEC':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,166,-345,-27,-28,-185,-343,166,166,166,-345,166,-292,-293,-294,-291,166,166,166,166,-295,-296,166,-345,-28,166,-186,-344,166,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,166,166,166,166,-345,166,-345,-28,-73,-69,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,166,-292,166,166,-345,166,166,-223,-222,166,166,-242,166,166,166,166,166,-87,-74,166,-238,-239,-241,166,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,166,-12,166,-292,166,166,166,-345,166,-345,-28,166,166,-224,166,-226,166,-86,-75,166,-237,-240,-345,-201,-345,-68,166,166,166,-345,-28,166,166,-292,-225,166,166,166,166,166,166,-11,-292,166,166,-230,-87,-74,-232,-233,166,-345,166,166,166,-231,-234,166,166,-236,-235,]),'INT_CONST_OCT':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,167,-345,-27,-28,-185,-343,167,167,167,-345,167,-292,-293,-294,-291,167,167,167,167,-295,-296,167,-345,-28,167,-186,-344,167,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,167,167,167,167,-345,167,-345,-28,-73,-69,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,167,-292,167,167,-345,167,167,-223,-222,167,167,-242,167,167,167,167,167,-87,-74,167,-238,-239,-241,167,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,167,-12,167,-292,167,167,167,-345,167,-345,-28,167,167,-224,167,-226,167,-86,-75,167,-237,-240,-345,-201,-345,-68,167,167,167,-345,-28,167,167,-292,-225,167,167,167,167,167,167,-11,-292,167,167,-230,-87,-74,-232,-233,167,-345,167,167,167,-231,-234,167,167,-236,-235,]),'INT_CONST_HEX':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,168,-345,-27,-28,-185,-343,168,168,168,-345,168,-292,-293,-294,-291,168,168,168,168,-295,-296,168,-345,-28,168,-186,-344,168,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,168,168,168,168,-345,168,-345,-28,-73,-69,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,168,-292,168,168,-345,168,168,-223,-222,168,168,-242,168,168,168,168,168,-87,-74,168,-238,-239,-241,168,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,168,-12,168,-292,168,168,168,-345,168,-345,-28,168,168,-224,168,-226,168,-86,-75,168,-237,-240,-345,-201,-345,-68,168,168,168,-345,-28,168,168,-292,-225,168,168,168,168,168,168,-11,-292,168,168,-230,-87,-74,-232,-233,168,-345,168,168,168,-231,-234,168,168,-236,-235,]),'INT_CONST_BIN':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,169,-345,-27,-28,-185,-343,169,169,169,-345,169,-292,-293,-294,-291,169,169,169,169,-295,-296,169,-345,-28,169,-186,-344,169,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,169,169,169,169,-345,169,-345,-28,-73,-69,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,169,-292,169,169,-345,169,169,-223,-222,169,169,-242,169,169,169,169,169,-87,-74,169,-238,-239,-241,169,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,169,-12,169,-292,169,169,169,-345,169,-345,-28,169,169,-224,169,-226,169,-86,-75,169,-237,-240,-345,-201,-345,-68,169,169,169,-345,-28,169,169,-292,-225,169,169,169,169,169,169,-11,-292,169,169,-230,-87,-74,-232,-233,169,-345,169,169,169,-231,-234,169,169,-236,-235,]),'INT_CONST_CHAR':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,170,-345,-27,-28,-185,-343,170,170,170,-345,170,-292,-293,-294,-291,170,170,170,170,-295,-296,170,-345,-28,170,-186,-344,170,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,170,170,170,170,-345,170,-345,-28,-73,-69,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,170,-292,170,170,-345,170,170,-223,-222,170,170,-242,170,170,170,170,170,-87,-74,170,-238,-239,-241,170,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,170,-12,170,-292,170,170,170,-345,170,-345,-28,170,170,-224,170,-226,170,-86,-75,170,-237,-240,-345,-201,-345,-68,170,170,170,-345,-28,170,170,-292,-225,170,170,170,170,170,170,-11,-292,170,170,-230,-87,-74,-232,-233,170,-345,170,170,170,-231,-234,170,170,-236,-235,]),'FLOAT_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,171,-345,-27,-28,-185,-343,171,171,171,-345,171,-292,-293,-294,-291,171,171,171,171,-295,-296,171,-345,-28,171,-186,-344,171,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,171,171,171,171,-345,171,-345,-28,-73,-69,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,171,-292,171,171,-345,171,171,-223,-222,171,171,-242,171,171,171,171,171,-87,-74,171,-238,-239,-241,171,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,171,-12,171,-292,171,171,171,-345,171,-345,-28,171,171,-224,171,-226,171,-86,-75,171,-237,-240,-345,-201,-345,-68,171,171,171,-345,-28,171,171,-292,-225,171,171,171,171,171,171,-11,-292,171,171,-230,-87,-74,-232,-233,171,-345,171,171,171,-231,-234,171,171,-236,-235,]),'HEX_FLOAT_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,172,-345,-27,-28,-185,-343,172,172,172,-345,172,-292,-293,-294,-291,172,172,172,172,-295,-296,172,-345,-28,172,-186,-344,172,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,172,172,172,172,-345,172,-345,-28,-73,-69,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,172,-292,172,172,-345,172,172,-223,-222,172,172,-242,172,172,172,172,172,-87,-74,172,-238,-239,-241,172,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,172,-12,172,-292,172,172,172,-345,172,-345,-28,172,172,-224,172,-226,172,-86,-75,172,-237,-240,-345,-201,-345,-68,172,172,172,-345,-28,172,172,-292,-225,172,172,172,172,172,172,-11,-292,172,172,-230,-87,-74,-232,-233,172,-345,172,172,172,-231,-234,172,172,-236,-235,]),'CHAR_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,173,-345,-27,-28,-185,-343,173,173,173,-345,173,-292,-293,-294,-291,173,173,173,173,-295,-296,173,-345,-28,173,-186,-344,173,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,173,173,173,173,-345,173,-345,-28,-73,-69,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,173,-292,173,173,-345,173,173,-223,-222,173,173,-242,173,173,173,173,173,-87,-74,173,-238,-239,-241,173,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,173,-12,173,-292,173,173,173,-345,173,-345,-28,173,173,-224,173,-226,173,-86,-75,173,-237,-240,-345,-201,-345,-68,173,173,173,-345,-28,173,173,-292,-225,173,173,173,173,173,173,-11,-292,173,173,-230,-87,-74,-232,-233,173,-345,173,173,173,-231,-234,173,173,-236,-235,]),'WCHAR_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,174,-345,-27,-28,-185,-343,174,174,174,-345,174,-292,-293,-294,-291,174,174,174,174,-295,-296,174,-345,-28,174,-186,-344,174,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,174,174,174,174,-345,174,-345,-28,-73,-69,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,174,-292,174,174,-345,174,174,-223,-222,174,174,-242,174,174,174,174,174,-87,-74,174,-238,-239,-241,174,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,174,-12,174,-292,174,174,174,-345,174,-345,-28,174,174,-224,174,-226,174,-86,-75,174,-237,-240,-345,-201,-345,-68,174,174,174,-345,-28,174,174,-292,-225,174,174,174,174,174,174,-11,-292,174,174,-230,-87,-74,-232,-233,174,-345,174,174,174,-231,-234,174,174,-236,-235,]),'U8CHAR_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,175,-345,-27,-28,-185,-343,175,175,175,-345,175,-292,-293,-294,-291,175,175,175,175,-295,-296,175,-345,-28,175,-186,-344,175,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,175,175,175,175,-345,175,-345,-28,-73,-69,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,175,-292,175,175,-345,175,175,-223,-222,175,175,-242,175,175,175,175,175,-87,-74,175,-238,-239,-241,175,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,175,-12,175,-292,175,175,175,-345,175,-345,-28,175,175,-224,175,-226,175,-86,-75,175,-237,-240,-345,-201,-345,-68,175,175,175,-345,-28,175,175,-292,-225,175,175,175,175,175,175,-11,-292,175,175,-230,-87,-74,-232,-233,175,-345,175,175,175,-231,-234,175,175,-236,-235,]),'U16CHAR_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,176,-345,-27,-28,-185,-343,176,176,176,-345,176,-292,-293,-294,-291,176,176,176,176,-295,-296,176,-345,-28,176,-186,-344,176,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,176,176,176,176,-345,176,-345,-28,-73,-69,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,176,-292,176,176,-345,176,176,-223,-222,176,176,-242,176,176,176,176,176,-87,-74,176,-238,-239,-241,176,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,176,-12,176,-292,176,176,176,-345,176,-345,-28,176,176,-224,176,-226,176,-86,-75,176,-237,-240,-345,-201,-345,-68,176,176,176,-345,-28,176,176,-292,-225,176,176,176,176,176,176,-11,-292,176,176,-230,-87,-74,-232,-233,176,-345,176,176,176,-231,-234,176,176,-236,-235,]),'U32CHAR_CONST':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,177,-345,-27,-28,-185,-343,177,177,177,-345,177,-292,-293,-294,-291,177,177,177,177,-295,-296,177,-345,-28,177,-186,-344,177,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,177,177,177,177,-345,177,-345,-28,-73,-69,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,177,-292,177,177,-345,177,177,-223,-222,177,177,-242,177,177,177,177,177,-87,-74,177,-238,-239,-241,177,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,177,-12,177,-292,177,177,177,-345,177,-345,-28,177,177,-224,177,-226,177,-86,-75,177,-237,-240,-345,-201,-345,-68,177,177,177,-345,-28,177,177,-292,-225,177,177,177,177,177,177,-11,-292,177,177,-230,-87,-74,-232,-233,177,-345,177,177,177,-231,-234,177,177,-236,-235,]),'STRING_LITERAL':([15,51,52,53,81,90,91,92,94,95,114,115,116,121,126,128,135,136,138,139,141,143,147,148,149,150,153,154,155,156,160,161,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,263,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,407,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,139,139,-345,-27,-28,-185,-343,139,139,139,-345,263,-333,139,263,-292,-293,-294,-291,139,139,139,139,-295,-296,139,-345,-28,139,-186,-344,139,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,139,139,139,139,-345,139,-345,-28,-73,-334,139,-69,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,139,-292,139,139,-345,139,139,-223,-222,139,139,-242,139,139,139,139,139,-87,-74,139,-238,-239,-241,139,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,139,-12,139,-292,139,139,139,263,-345,139,-345,-28,139,139,-224,139,-226,139,-86,-75,139,-237,-240,-345,-201,-345,-68,139,139,139,-345,-28,139,139,-292,-225,139,139,139,139,139,139,-11,-292,139,139,-230,-87,-74,-232,-233,139,-345,139,139,139,-231,-234,139,139,-236,-235,]),'WSTRING_LITERAL':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,164,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,178,-345,-27,-28,-185,-343,178,178,178,-345,178,-292,-293,-294,-291,178,178,178,178,-295,-296,300,-335,-336,-337,-338,178,-345,-28,178,-186,-344,178,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,178,178,178,178,-345,178,-345,-28,-73,-69,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,178,-339,-340,-341,-342,-292,178,178,-345,178,178,-223,-222,178,178,-242,178,178,178,178,178,-87,-74,178,-238,-239,-241,178,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,178,-12,178,-292,178,178,178,-345,178,-345,-28,178,178,-224,178,-226,178,-86,-75,178,-237,-240,-345,-201,-345,-68,178,178,178,-345,-28,178,178,-292,-225,178,178,178,178,178,178,-11,-292,178,178,-230,-87,-74,-232,-233,178,-345,178,178,178,-231,-234,178,178,-236,-235,]),'U8STRING_LITERAL':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,164,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,179,-345,-27,-28,-185,-343,179,179,179,-345,179,-292,-293,-294,-291,179,179,179,179,-295,-296,301,-335,-336,-337,-338,179,-345,-28,179,-186,-344,179,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,179,179,179,179,-345,179,-345,-28,-73,-69,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,179,-339,-340,-341,-342,-292,179,179,-345,179,179,-223,-222,179,179,-242,179,179,179,179,179,-87,-74,179,-238,-239,-241,179,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,179,-12,179,-292,179,179,179,-345,179,-345,-28,179,179,-224,179,-226,179,-86,-75,179,-237,-240,-345,-201,-345,-68,179,179,179,-345,-28,179,179,-292,-225,179,179,179,179,179,179,-11,-292,179,179,-230,-87,-74,-232,-233,179,-345,179,179,179,-231,-234,179,179,-236,-235,]),'U16STRING_LITERAL':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,164,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,180,-345,-27,-28,-185,-343,180,180,180,-345,180,-292,-293,-294,-291,180,180,180,180,-295,-296,302,-335,-336,-337,-338,180,-345,-28,180,-186,-344,180,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,180,180,180,180,-345,180,-345,-28,-73,-69,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,180,-339,-340,-341,-342,-292,180,180,-345,180,180,-223,-222,180,180,-242,180,180,180,180,180,-87,-74,180,-238,-239,-241,180,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,180,-12,180,-292,180,180,180,-345,180,-345,-28,180,180,-224,180,-226,180,-86,-75,180,-237,-240,-345,-201,-345,-68,180,180,180,-345,-28,180,180,-292,-225,180,180,180,180,180,180,-11,-292,180,180,-230,-87,-74,-232,-233,180,-345,180,180,180,-231,-234,180,180,-236,-235,]),'U32STRING_LITERAL':([15,51,52,53,81,90,91,94,95,114,115,116,121,126,128,135,136,141,147,148,149,150,153,154,155,156,160,161,164,178,179,180,181,182,183,184,195,197,208,221,222,223,224,225,226,227,228,229,230,231,232,234,238,242,247,256,257,258,259,262,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,300,301,302,303,306,309,310,323,332,347,355,356,358,360,361,362,365,366,367,369,370,371,372,374,375,377,378,379,380,381,382,383,384,385,386,387,388,389,392,393,394,397,400,401,402,405,448,455,457,458,459,469,471,472,473,476,481,482,484,485,486,489,491,500,501,502,505,512,513,514,521,522,524,528,529,530,531,532,533,536,537,547,548,549,558,559,560,561,562,565,568,571,578,580,587,588,590,592,593,594,],[-71,-131,-132,-133,-134,-90,-72,181,-345,-27,-28,-185,-343,181,181,181,-345,181,-292,-293,-294,-291,181,181,181,181,-295,-296,303,-335,-336,-337,-338,181,-345,-28,181,-186,-344,181,-221,-219,-220,-78,-79,-80,-81,-82,-83,-84,-85,181,181,181,181,-345,181,-345,-28,-73,-69,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,181,-339,-340,-341,-342,-292,181,181,-345,181,181,-223,-222,181,181,-242,181,181,181,181,181,-87,-74,181,-238,-239,-241,181,-249,-250,-251,-252,-253,-254,-255,-256,-257,-258,-259,-11,181,-12,181,-292,181,181,181,-345,181,-345,-28,181,181,-224,181,-226,181,-86,-75,181,-237,-240,-345,-201,-345,-68,181,181,181,-345,-28,181,181,-292,-225,181,181,181,181,181,181,-11,-292,181,181,-230,-87,-74,-232,-233,181,-345,181,181,181,-231,-234,181,181,-236,-235,]),'ELSE':([15,91,208,225,226,227,228,229,230,232,262,267,355,358,360,361,370,371,374,375,377,471,472,473,481,482,485,486,501,528,558,559,560,561,562,587,588,593,594,],[-71,-72,-344,-78,-79,-80,-81,-82,-83,-85,-73,-69,-223,-227,-229,-242,-87,-84,-238,-239,-241,-224,-228,-226,-86,-84,-237,-240,-68,-225,-230,578,-84,-232,-233,-231,-234,-236,-235,]),'PPPRAGMASTR':([15,],[91,]),'EQUALS':([19,28,73,86,87,88,89,97,111,130,132,139,140,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,202,208,233,250,252,263,291,292,293,295,296,297,300,301,302,303,311,312,395,396,403,404,406,429,431,432,433,434,439,440,492,494,495,496,499,503,504,507,508,510,511,538,539,540,567,569,582,],[-52,-29,-181,135,-182,-54,-37,-53,195,-181,-55,-333,-30,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,332,-344,-320,379,-38,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-35,-36,491,-202,-43,-44,-313,-300,-301,-302,-303,-304,-31,-34,-203,-205,-39,-42,-283,-298,-299,-289,-290,-32,-33,-204,-40,-41,-307,-314,-308,]),'COMMA':([19,24,25,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59,60,73,74,75,76,77,78,81,84,85,86,87,88,89,97,104,106,108,110,111,113,114,115,116,118,119,122,123,130,132,139,140,142,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,187,189,190,191,192,196,197,200,201,202,206,208,212,214,216,233,239,248,249,250,252,253,254,255,263,265,291,292,293,295,296,297,300,301,302,303,311,312,315,316,317,318,319,320,321,324,325,326,327,328,329,330,331,334,336,337,340,341,342,344,345,346,348,349,350,352,353,354,376,391,403,404,406,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,438,439,440,444,445,446,447,461,462,463,464,465,466,470,474,475,477,478,479,487,488,490,495,496,499,503,504,507,508,510,511,517,518,520,526,527,535,539,540,541,542,543,550,551,552,555,556,557,563,566,567,569,572,573,576,577,582,584,585,586,],[-52,-128,-102,-29,-107,-345,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-181,-98,-99,-100,-101,-104,-134,134,-135,-137,-182,-54,-37,-53,-103,-129,194,-139,-141,-183,-27,-28,-185,-169,-170,-149,-150,-181,-55,-333,-30,266,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,313,314,-189,-194,-345,-184,-186,331,-174,-179,-152,-344,-145,-147,-345,-320,365,-243,-247,-282,-38,-136,-138,-196,-334,365,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-35,-36,-191,-192,-193,-207,-56,-1,-2,-45,-209,-140,-142,331,331,-171,-175,-154,-156,-151,-143,-144,-148,468,-164,-166,-146,-130,-206,-207,-177,-178,365,489,-43,-44,-313,365,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,365,505,-300,-318,-301,-302,-303,-304,509,-31,-34,-190,-195,-57,-208,-172,-173,-176,-180,-153,-155,-168,365,-245,-244,365,365,-248,-197,-199,-39,-42,-283,-298,-299,-289,-290,-32,-33,-210,-216,-214,-165,-167,-198,-40,-41,568,-262,-319,-50,-51,-212,-211,-213,-215,365,-200,-307,-314,-46,-49,-217,-218,-308,365,-47,-48,]),'RPAREN':([19,24,25,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59,60,74,75,76,77,78,81,88,89,93,96,97,104,106,113,114,115,116,118,119,122,123,132,133,137,138,139,140,142,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,185,186,187,188,189,190,191,192,196,197,206,208,212,214,215,216,217,218,239,248,249,250,252,260,261,263,264,265,288,291,292,293,295,296,297,300,301,302,303,311,312,315,316,317,318,319,320,321,322,324,325,330,334,336,337,340,341,342,348,349,350,351,352,353,354,355,357,363,364,403,404,406,407,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,428,429,430,431,432,433,434,435,436,437,439,440,443,444,445,446,447,449,450,451,452,453,454,460,461,462,465,466,474,475,477,478,479,487,495,496,499,503,504,507,508,510,511,515,516,517,518,520,525,539,540,542,543,544,545,550,551,552,555,556,557,563,565,567,569,572,573,576,577,580,581,582,583,585,586,589,591,],[-52,-128,-102,-29,-107,-345,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-98,-99,-100,-101,-104,-134,-54,-37,140,-345,-53,-103,-129,-183,-27,-28,-185,-169,-170,-149,-150,-55,252,-345,262,-333,-30,267,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,311,312,-187,-17,-18,-189,-194,-345,-184,-186,-152,-344,-145,-147,349,-345,353,354,-14,-243,-247,-282,-38,403,404,-334,405,406,429,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-35,-36,-191,-192,-193,-207,-56,-1,-2,-345,-45,-209,-171,-154,-156,-151,-143,-144,-148,-146,-130,-206,-345,-207,-177,-178,-223,-13,475,476,-43,-44,-313,501,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,504,-300,-318,-301,-302,-303,-304,506,507,508,-31,-34,-188,-190,-195,-57,-208,-345,517,518,-207,-23,-24,-345,-172,-173,-153,-155,529,-245,-244,530,531,-248,-39,-42,-283,-298,-299,-289,-290,-32,-33,550,551,-210,-216,-214,557,-40,-41,-262,-319,569,-315,-50,-51,-212,-211,-213,-215,579,-345,-307,-314,-46,-49,-217,-218,-345,590,-308,-316,-47,-48,592,-317,]),'COLON':([19,24,28,31,32,33,35,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,81,87,88,89,97,106,118,119,122,123,130,132,139,140,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,206,208,209,212,214,233,235,248,249,250,252,263,291,292,293,295,296,297,300,301,302,303,311,312,330,334,336,337,340,341,342,346,348,349,353,354,359,403,404,406,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,439,440,461,462,465,466,468,475,477,487,495,496,499,503,504,507,508,510,511,539,540,542,567,569,582,],[-52,-128,-29,-125,-126,-127,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-131,-132,-133,-134,-182,-54,-37,-53,-129,-169,-170,-149,-150,-181,-55,-333,-30,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-152,-344,347,-145,-147,358,360,-243,-247,-282,-38,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-35,-36,-171,-154,-156,-151,-143,-144,-148,469,-146,-130,-177,-178,472,-43,-44,-313,502,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-300,-301,-302,-303,-304,-31,-34,-172,-173,-153,-155,347,-245,-244,-248,-39,-42,-283,-298,-299,-289,-290,-32,-33,-40,-41,-262,-307,-314,-308,]),'LBRACKET':([19,24,25,28,29,30,31,32,33,34,35,38,39,40,41,42,43,44,45,46,47,48,49,51,52,53,54,55,56,57,58,59,60,74,75,76,77,78,81,88,89,97,104,106,113,114,115,116,118,119,121,122,123,132,139,140,143,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,192,196,197,206,208,212,214,216,233,252,256,263,291,292,300,301,302,303,311,312,318,319,322,324,325,330,334,336,337,340,341,342,348,349,351,352,353,354,395,396,403,404,406,429,431,432,433,434,439,440,446,447,452,461,462,465,466,489,492,494,495,496,500,503,504,510,511,517,518,520,538,539,540,544,545,550,551,552,555,556,557,567,568,569,572,573,576,577,582,583,585,586,591,],[95,-128,-102,-29,-107,-345,-125,-126,-127,-129,-246,-113,-114,-115,-116,-117,-118,-119,-120,-121,-122,-123,-124,-131,-132,-133,-105,-106,-108,-109,-110,-111,-112,-98,-99,-100,-101,-104,-134,136,-37,95,-103,-129,-183,-27,-28,-185,-169,-170,-343,-149,-150,136,-333,-30,-311,287,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,323,-184,-186,-152,-344,-145,-147,323,-320,-38,397,-334,-305,-306,-339,-340,-341,-342,-35,-36,323,448,323,-45,459,-171,-154,-156,-151,-143,-144,-148,-146,-130,323,323,-177,-178,397,-202,-43,-44,-313,-300,-301,-302,-303,-304,-31,-34,448,459,323,-172,-173,-153,-155,397,-203,-205,-39,-42,397,-298,-299,-32,-33,-210,-216,-214,-204,-40,-41,571,-315,-50,-51,-212,-211,-213,-215,-307,397,-314,-46,-49,-217,-218,-308,-316,-47,-48,-317,]),'RBRACKET':([51,52,53,81,95,114,116,136,139,143,144,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,184,197,208,248,249,250,257,259,263,291,292,293,295,296,297,300,301,302,303,305,306,307,308,323,399,400,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,429,431,432,433,434,441,442,448,455,456,458,459,475,477,487,493,497,498,499,503,504,507,508,512,514,519,523,524,542,546,547,553,554,567,569,574,575,582,584,],[-131,-132,-133,-134,-345,-27,-185,-345,-333,-311,-260,-261,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-345,-28,-186,-344,-243,-247,-282,-345,-28,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,439,440,-3,-4,-345,495,496,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,503,-300,-301,-302,-303,-304,510,511,-345,-345,520,-28,-345,-245,-244,-248,538,539,540,-283,-298,-299,-289,-290,-345,-28,552,555,556,-262,572,573,576,577,-307,-314,585,586,-308,591,]),'PERIOD':([121,139,143,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,256,263,291,292,300,301,302,303,395,396,406,429,431,432,433,434,489,492,494,500,503,504,538,544,545,567,568,569,582,583,591,],[-343,-333,-311,289,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,398,-334,-305,-306,-339,-340,-341,-342,398,-202,-313,-300,-301,-302,-303,-304,398,-203,-205,398,-298,-299,-204,570,-315,-307,398,-314,-308,-316,-317,]),'ARROW':([139,143,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,263,291,292,300,301,302,303,406,429,431,432,433,434,503,504,567,569,582,],[-333,-311,290,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-334,-305,-306,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-298,-299,-307,-314,-308,]),'CONDOP':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,268,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'DIVIDE':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,270,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,270,270,270,270,270,270,270,270,270,270,270,270,270,270,270,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'MOD':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,271,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,271,271,271,271,271,271,271,271,271,271,271,271,271,271,271,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'RSHIFT':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,274,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,274,274,274,274,274,274,274,274,274,274,274,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LSHIFT':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,275,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,275,275,275,275,275,275,275,275,275,275,275,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LT':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,276,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,276,276,276,276,276,276,276,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LE':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,277,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,277,277,277,277,277,277,277,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'GE':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,278,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,278,278,278,278,278,278,278,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'GT':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,279,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,279,279,279,279,279,279,279,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'EQ':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,280,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,280,280,280,280,280,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'NE':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,281,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,281,281,281,281,281,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'OR':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,283,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,283,283,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'XOR':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,284,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,284,-279,284,284,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LAND':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,285,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,285,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LOR':([139,143,145,146,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,286,-263,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,-282,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-264,-265,-266,-267,-268,-269,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'XOREQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,380,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'TIMESEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,381,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'DIVEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,382,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'MODEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,383,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'PLUSEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,384,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'MINUSEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,385,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'LSHIFTEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,386,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'RSHIFTEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,387,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'ANDEQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,388,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'OREQUAL':([139,143,151,152,158,159,162,163,164,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,208,233,250,263,291,292,293,295,296,297,300,301,302,303,406,429,431,432,433,434,499,503,504,507,508,567,569,582,],[-333,-311,-282,-284,-297,-320,-309,-310,-312,-321,-322,-323,-324,-325,-326,-327,-328,-329,-330,-331,-332,-335,-336,-337,-338,-344,-320,389,-334,-305,-306,-285,-286,-287,-288,-339,-340,-341,-342,-313,-300,-301,-302,-303,-304,-283,-298,-299,-289,-290,-307,-314,-308,]),'ELLIPSIS':([313,],[443,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'translation_unit_or_empty':([0,],[1,]),'translation_unit':([0,],[2,]),'empty':([0,11,12,21,22,23,26,27,30,34,69,70,71,73,95,96,101,128,136,137,182,183,192,209,216,221,242,256,257,258,322,323,351,358,360,369,372,448,449,455,457,459,460,472,484,489,500,512,513,529,530,531,533,565,568,578,580,590,592,],[3,66,83,99,99,99,107,99,114,99,83,107,99,66,114,188,99,220,114,188,307,114,320,343,320,357,357,392,307,114,453,114,453,357,357,357,357,114,188,307,114,307,453,357,357,537,537,307,114,357,357,357,357,357,537,357,357,357,357,]),'external_declaration':([0,2,],[4,64,]),'function_definition':([0,2,],[5,5,]),'declaration':([0,2,11,67,73,128,221,372,],[6,6,68,129,68,223,223,484,]),'pp_directive':([0,2,],[7,7,]),'pppragma_directive':([0,2,124,128,203,204,205,221,242,333,335,358,360,369,472,529,530,531,578,590,592,],[8,8,211,231,211,211,211,231,371,211,211,371,371,482,371,560,371,371,371,371,371,]),'static_assert':([0,2,128,221,242,358,360,369,472,529,530,531,578,590,592,],[10,10,232,232,232,232,232,232,232,232,232,232,232,232,232,]),'id_declarator':([0,2,12,17,26,69,70,82,134,192,194,209,322,468,],[11,11,73,93,111,130,111,93,130,315,130,130,93,130,]),'declaration_specifiers':([0,2,11,67,73,96,128,137,221,313,322,351,372,449,460,],[12,12,69,69,69,192,69,192,69,192,192,192,69,192,192,]),'decl_body':([0,2,11,67,73,128,221,372,],[13,13,13,13,13,13,13,13,]),'direct_id_declarator':([0,2,12,17,20,26,69,70,80,82,134,192,194,209,318,322,452,468,],[19,19,19,19,97,19,19,19,97,19,19,19,19,19,97,19,97,19,]),'pointer':([0,2,12,17,26,69,70,82,113,134,192,194,209,216,322,351,468,],[20,20,80,20,20,80,20,80,196,80,318,80,80,352,452,352,80,]),'type_qualifier':([0,2,11,12,21,22,23,27,30,34,67,69,71,73,95,96,101,115,124,125,126,128,136,137,141,183,184,192,203,204,205,209,213,216,221,238,258,259,294,298,299,304,313,322,323,333,335,351,372,448,449,457,458,460,513,514,],[21,21,21,74,21,21,21,21,116,21,21,74,21,21,116,21,21,197,116,116,116,21,116,21,116,116,197,74,116,116,116,341,197,341,21,116,116,197,116,116,116,116,21,21,116,116,116,21,21,116,21,116,197,21,116,197,]),'storage_class_specifier':([0,2,11,12,21,22,23,27,34,67,69,71,73,96,101,128,137,192,221,313,322,351,372,449,460,],[22,22,22,75,22,22,22,22,22,22,75,22,22,22,22,22,22,75,22,22,22,22,22,22,22,]),'function_specifier':([0,2,11,12,21,22,23,27,34,67,69,71,73,96,101,128,137,192,221,313,322,351,372,449,460,],[23,23,23,76,23,23,23,23,23,23,76,23,23,23,23,23,23,76,23,23,23,23,23,23,23,]),'type_specifier_no_typeid':([0,2,11,12,26,67,69,70,73,96,124,125,126,128,137,141,192,193,203,204,205,209,213,216,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[24,24,24,77,24,24,77,24,24,24,24,24,24,24,24,24,77,24,24,24,24,340,24,340,24,24,24,24,24,24,24,24,24,24,24,24,24,24,]),'type_specifier':([0,2,11,26,67,70,73,96,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[25,25,25,104,25,104,25,25,212,212,212,25,25,212,104,212,212,212,348,25,212,212,212,212,212,25,25,212,212,25,25,25,25,]),'declaration_specifiers_no_type':([0,2,11,21,22,23,27,34,67,71,73,96,101,128,137,221,313,322,351,372,449,460,],[26,26,70,100,100,100,100,100,70,100,70,193,100,70,193,70,193,193,193,70,193,193,]),'alignment_specifier':([0,2,11,12,21,22,23,27,34,67,69,71,73,96,101,124,125,126,128,137,141,192,203,204,205,209,216,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[27,27,27,78,27,27,27,27,27,27,78,27,27,27,27,214,214,214,27,27,214,78,214,214,214,342,342,27,214,214,214,214,214,27,27,214,214,27,27,27,27,]),'typedef_name':([0,2,11,26,67,70,73,96,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,31,]),'enum_specifier':([0,2,11,26,67,70,73,96,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,]),'struct_or_union_specifier':([0,2,11,26,67,70,73,96,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,33,]),'atomic_specifier':([0,2,11,21,22,23,26,27,34,67,70,71,73,96,101,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[34,34,71,101,101,101,106,101,101,71,106,101,71,34,101,106,106,106,71,34,106,106,106,106,106,106,71,106,106,106,106,106,34,34,106,106,34,71,34,34,]),'struct_or_union':([0,2,11,26,67,70,73,96,124,125,126,128,137,141,193,203,204,205,213,221,238,294,298,299,304,313,322,333,335,351,372,449,460,],[37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,37,]),'declaration_list_opt':([11,73,],[65,131,]),'declaration_list':([11,73,],[67,67,]),'init_declarator_list_opt':([12,69,],[79,79,]),'init_declarator_list':([12,69,],[84,84,]),'init_declarator':([12,69,134,194,],[85,85,253,326,]),'declarator':([12,69,134,194,209,468,],[86,86,86,86,346,346,]),'typeid_declarator':([12,69,82,134,194,209,468,],[87,87,133,87,87,87,87,]),'direct_typeid_declarator':([12,69,80,82,134,194,209,468,],[88,88,132,88,88,88,88,88,]),'declaration_specifiers_no_type_opt':([21,22,23,27,34,71,101,],[98,102,103,112,117,117,117,]),'id_init_declarator_list_opt':([26,70,],[105,105,]),'id_init_declarator_list':([26,70,],[108,108,]),'id_init_declarator':([26,70,],[110,110,]),'type_qualifier_list_opt':([30,95,136,183,258,323,448,457,513,],[113,182,257,309,401,455,512,521,548,]),'type_qualifier_list':([30,95,124,125,126,136,141,183,203,204,205,238,258,294,298,299,304,323,333,335,448,457,513,],[115,184,213,213,213,259,213,115,213,213,213,213,115,213,213,213,213,458,213,213,514,115,115,]),'brace_open':([36,37,65,118,119,122,123,128,131,135,195,221,238,242,358,360,369,393,405,472,476,506,507,529,530,531,536,578,590,592,],[120,124,128,198,199,203,204,128,128,256,256,128,128,128,128,128,128,256,500,128,500,500,500,128,128,128,256,128,128,128,]),'compound_statement':([65,128,131,221,238,242,358,360,369,472,529,530,531,578,590,592,],[127,227,251,227,363,227,227,227,227,227,227,227,227,227,227,227,]),'unified_string_literal':([92,94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,266,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[138,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,407,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,143,]),'constant_expression':([94,126,234,332,347,397,469,],[142,218,359,464,470,493,527,]),'conditional_expression':([94,126,128,135,141,182,195,221,234,238,242,247,257,268,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,455,459,469,472,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[144,144,249,249,249,249,249,249,144,249,249,249,249,249,249,249,249,249,249,249,144,144,249,249,249,249,249,249,249,249,249,249,144,249,249,249,249,144,249,249,542,249,249,249,249,249,249,249,249,249,249,249,249,249,249,249,249,249,249,]),'binary_expression':([94,126,128,135,141,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,455,459,469,472,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[145,145,145,145,145,145,145,145,145,145,145,145,145,145,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,145,]),'cast_expression':([94,126,128,135,141,155,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[146,146,146,146,146,296,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,499,146,146,146,146,499,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,146,]),'unary_expression':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[151,151,250,250,250,293,295,151,297,250,250,250,151,250,250,250,250,250,151,151,151,151,151,151,151,151,151,151,151,151,151,151,151,151,151,151,250,250,250,250,250,250,151,151,250,250,250,250,250,250,250,250,250,250,151,250,250,151,250,250,151,250,151,250,151,250,250,250,250,250,250,250,250,250,250,250,250,250,250,250,250,250,250,]),'postfix_expression':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,152,]),'unary_operator':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,155,]),'primary_expression':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,158,]),'identifier':([94,96,126,128,135,137,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,314,332,347,358,360,362,365,366,367,369,372,378,393,397,398,401,402,405,449,455,459,469,472,476,484,502,505,509,512,521,522,529,530,531,532,533,536,548,549,565,570,571,578,580,590,592,],[162,191,162,162,162,191,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,162,445,162,162,162,162,162,162,162,162,162,162,162,162,162,494,162,162,162,191,162,162,162,162,162,162,162,162,545,162,162,162,162,162,162,162,162,162,162,162,162,583,162,162,162,162,162,]),'constant':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,163,]),'unified_wstring_literal':([94,126,128,135,141,153,154,155,156,182,195,221,234,238,242,247,257,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,294,298,309,310,332,347,358,360,362,365,366,367,369,372,378,393,397,401,402,405,455,459,469,472,476,484,502,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,164,]),'parameter_type_list':([96,137,322,351,449,460,],[185,260,454,454,515,454,]),'identifier_list_opt':([96,137,449,],[186,261,516,]),'parameter_list':([96,137,322,351,449,460,],[187,187,187,187,187,187,]),'identifier_list':([96,137,449,],[189,189,189,]),'parameter_declaration':([96,137,313,322,351,449,460,],[190,190,444,190,190,190,190,]),'enumerator_list':([120,198,199,],[200,328,329,]),'enumerator':([120,198,199,331,],[201,201,201,463,]),'struct_declaration_list':([124,203,204,],[205,333,335,]),'brace_close':([124,200,203,204,205,219,328,329,333,335,390,489,541,568,],[206,330,334,336,337,355,461,462,465,466,488,535,567,582,]),'struct_declaration':([124,203,204,205,333,335,],[207,207,207,338,338,338,]),'specifier_qualifier_list':([124,125,126,141,203,204,205,238,294,298,299,304,333,335,],[209,216,216,216,209,209,209,216,216,216,216,216,209,209,]),'type_name':([125,126,141,238,294,298,299,304,],[215,217,264,364,435,436,437,438,]),'block_item_list_opt':([128,],[219,]),'block_item_list':([128,],[221,]),'block_item':([128,221,],[222,356,]),'statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[224,224,370,370,370,481,370,559,370,370,370,370,370,]),'labeled_statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[225,225,225,225,225,225,225,225,225,225,225,225,225,]),'expression_statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[226,226,226,226,226,226,226,226,226,226,226,226,226,]),'selection_statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[228,228,228,228,228,228,228,228,228,228,228,228,228,]),'iteration_statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[229,229,229,229,229,229,229,229,229,229,229,229,229,]),'jump_statement':([128,221,242,358,360,369,472,529,530,531,578,590,592,],[230,230,230,230,230,230,230,230,230,230,230,230,230,]),'expression_opt':([128,221,242,358,360,369,372,472,484,529,530,531,533,565,578,580,590,592,],[236,236,236,236,236,236,483,236,534,236,236,236,564,581,236,589,236,236,]),'expression':([128,141,221,238,242,247,268,287,294,298,358,360,362,366,367,369,372,472,484,529,530,531,532,533,565,571,578,580,590,592,],[239,265,239,265,239,376,408,427,265,265,239,239,474,478,479,239,239,239,239,239,239,239,563,239,239,584,239,239,239,239,]),'assignment_expression':([128,135,141,182,195,221,238,242,247,257,268,287,288,294,298,309,310,358,360,362,365,366,367,369,372,378,393,401,402,455,459,472,484,505,512,521,522,529,530,531,532,533,536,548,549,565,571,578,580,590,592,],[248,255,248,308,255,248,248,248,248,308,248,248,430,248,248,441,442,248,248,248,477,248,248,248,248,487,255,497,498,308,308,248,248,543,308,553,554,248,248,248,248,248,255,574,575,248,248,248,248,248,248,]),'initializer':([135,195,393,536,],[254,327,490,566,]),'assignment_expression_opt':([182,257,455,459,512,],[305,399,519,523,546,]),'typeid_noparen_declarator':([192,],[316,]),'abstract_declarator_opt':([192,216,],[317,350,]),'direct_typeid_noparen_declarator':([192,318,],[319,446,]),'abstract_declarator':([192,216,322,351,],[321,321,450,450,]),'direct_abstract_declarator':([192,216,318,322,351,352,452,],[325,325,447,325,325,447,447,]),'struct_declarator_list_opt':([209,],[339,]),'struct_declarator_list':([209,],[344,]),'struct_declarator':([209,468,],[345,526,]),'pragmacomp_or_statement':([242,358,360,472,529,530,531,578,590,592,],[368,471,473,528,558,561,562,587,593,594,]),'pppragma_directive_list':([242,358,360,472,529,530,531,578,590,592,],[369,369,369,369,369,369,369,369,369,369,]),'assignment_operator':([250,],[378,]),'initializer_list_opt':([256,],[390,]),'initializer_list':([256,500,],[391,541,]),'designation_opt':([256,489,500,568,],[393,536,393,536,]),'designation':([256,489,500,568,],[394,394,394,394,]),'designator_list':([256,489,500,568,],[395,395,395,395,]),'designator':([256,395,489,500,568,],[396,492,396,396,396,]),'argument_expression_list':([288,],[428,]),'parameter_type_list_opt':([322,351,460,],[451,451,525,]),'offsetof_member_designator':([509,],[544,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> translation_unit_or_empty","S'",1,None,None,None),
  ('abstract_declarator_opt -> empty','abstract_declarator_opt',1,'p_abstract_declarator_opt','plyparser.py',43),
  ('abstract_declarator_opt -> abstract_declarator','abstract_declarator_opt',1,'p_abstract_declarator_opt','plyparser.py',44),
  ('assignment_expression_opt -> empty','assignment_expression_opt',1,'p_assignment_expression_opt','plyparser.py',43),
  ('assignment_expression_opt -> assignment_expression','assignment_expression_opt',1,'p_assignment_expression_opt','plyparser.py',44),
  ('block_item_list_opt -> empty','block_item_list_opt',1,'p_block_item_list_opt','plyparser.py',43),
  ('block_item_list_opt -> block_item_list','block_item_list_opt',1,'p_block_item_list_opt','plyparser.py',44),
  ('declaration_list_opt -> empty','declaration_list_opt',1,'p_declaration_list_opt','plyparser.py',43),
  ('declaration_list_opt -> declaration_list','declaration_list_opt',1,'p_declaration_list_opt','plyparser.py',44),
  ('declaration_specifiers_no_type_opt -> empty','declaration_specifiers_no_type_opt',1,'p_declaration_specifiers_no_type_opt','plyparser.py',43),
  ('declaration_specifiers_no_type_opt -> declaration_specifiers_no_type','declaration_specifiers_no_type_opt',1,'p_declaration_specifiers_no_type_opt','plyparser.py',44),
  ('designation_opt -> empty','designation_opt',1,'p_designation_opt','plyparser.py',43),
  ('designation_opt -> designation','designation_opt',1,'p_designation_opt','plyparser.py',44),
  ('expression_opt -> empty','expression_opt',1,'p_expression_opt','plyparser.py',43),
  ('expression_opt -> expression','expression_opt',1,'p_expression_opt','plyparser.py',44),
  ('id_init_declarator_list_opt -> empty','id_init_declarator_list_opt',1,'p_id_init_declarator_list_opt','plyparser.py',43),
  ('id_init_declarator_list_opt -> id_init_declarator_list','id_init_declarator_list_opt',1,'p_id_init_declarator_list_opt','plyparser.py',44),
  ('identifier_list_opt -> empty','identifier_list_opt',1,'p_identifier_list_opt','plyparser.py',43),
  ('identifier_list_opt -> identifier_list','identifier_list_opt',1,'p_identifier_list_opt','plyparser.py',44),
  ('init_declarator_list_opt -> empty','init_declarator_list_opt',1,'p_init_declarator_list_opt','plyparser.py',43),
  ('init_declarator_list_opt -> init_declarator_list','init_declarator_list_opt',1,'p_init_declarator_list_opt','plyparser.py',44),
  ('initializer_list_opt -> empty','initializer_list_opt',1,'p_initializer_list_opt','plyparser.py',43),
  ('initializer_list_opt -> initializer_list','initializer_list_opt',1,'p_initializer_list_opt','plyparser.py',44),
  ('parameter_type_list_opt -> empty','parameter_type_list_opt',1,'p_parameter_type_list_opt','plyparser.py',43),
  ('parameter_type_list_opt -> parameter_type_list','parameter_type_list_opt',1,'p_parameter_type_list_opt','plyparser.py',44),
  ('struct_declarator_list_opt -> empty','struct_declarator_list_opt',1,'p_struct_declarator_list_opt','plyparser.py',43),
  ('struct_declarator_list_opt -> struct_declarator_list','struct_declarator_list_opt',1,'p_struct_declarator_list_opt','plyparser.py',44),
  ('type_qualifier_list_opt -> empty','type_qualifier_list_opt',1,'p_type_qualifier_list_opt','plyparser.py',43),
  ('type_qualifier_list_opt -> type_qualifier_list','type_qualifier_list_opt',1,'p_type_qualifier_list_opt','plyparser.py',44),
  ('direct_id_declarator -> ID','direct_id_declarator',1,'p_direct_id_declarator_1','plyparser.py',126),
  ('direct_id_declarator -> LPAREN id_declarator RPAREN','direct_id_declarator',3,'p_direct_id_declarator_2','plyparser.py',126),
  ('direct_id_declarator -> direct_id_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET','direct_id_declarator',5,'p_direct_id_declarator_3','plyparser.py',126),
  ('direct_id_declarator -> direct_id_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET','direct_id_declarator',6,'p_direct_id_declarator_4','plyparser.py',126),
  ('direct_id_declarator -> direct_id_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET','direct_id_declarator',6,'p_direct_id_declarator_4','plyparser.py',127),
  ('direct_id_declarator -> direct_id_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET','direct_id_declarator',5,'p_direct_id_declarator_5','plyparser.py',126),
  ('direct_id_declarator -> direct_id_declarator LPAREN parameter_type_list RPAREN','direct_id_declarator',4,'p_direct_id_declarator_6','plyparser.py',126),
  ('direct_id_declarator -> direct_id_declarator LPAREN identifier_list_opt RPAREN','direct_id_declarator',4,'p_direct_id_declarator_6','plyparser.py',127),
  ('direct_typeid_declarator -> TYPEID','direct_typeid_declarator',1,'p_direct_typeid_declarator_1','plyparser.py',126),
  ('direct_typeid_declarator -> LPAREN typeid_declarator RPAREN','direct_typeid_declarator',3,'p_direct_typeid_declarator_2','plyparser.py',126),
  ('direct_typeid_declarator -> direct_typeid_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET','direct_typeid_declarator',5,'p_direct_typeid_declarator_3','plyparser.py',126),
  ('direct_typeid_declarator -> direct_typeid_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET','direct_typeid_declarator',6,'p_direct_typeid_declarator_4','plyparser.py',126),
  ('direct_typeid_declarator -> direct_typeid_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET','direct_typeid_declarator',6,'p_direct_typeid_declarator_4','plyparser.py',127),
  ('direct_typeid_declarator -> direct_typeid_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET','direct_typeid_declarator',5,'p_direct_typeid_declarator_5','plyparser.py',126),
  ('direct_typeid_declarator -> direct_typeid_declarator LPAREN parameter_type_list RPAREN','direct_typeid_declarator',4,'p_direct_typeid_declarator_6','plyparser.py',126),
  ('direct_typeid_declarator -> direct_typeid_declarator LPAREN identifier_list_opt RPAREN','direct_typeid_declarator',4,'p_direct_typeid_declarator_6','plyparser.py',127),
  ('direct_typeid_noparen_declarator -> TYPEID','direct_typeid_noparen_declarator',1,'p_direct_typeid_noparen_declarator_1','plyparser.py',126),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET','direct_typeid_noparen_declarator',5,'p_direct_typeid_noparen_declarator_3','plyparser.py',126),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET','direct_typeid_noparen_declarator',6,'p_direct_typeid_noparen_declarator_4','plyparser.py',126),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET','direct_typeid_noparen_declarator',6,'p_direct_typeid_noparen_declarator_4','plyparser.py',127),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LBRACKET type_qualifier_list_opt TIMES RBRACKET','direct_typeid_noparen_declarator',5,'p_direct_typeid_noparen_declarator_5','plyparser.py',126),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LPAREN parameter_type_list RPAREN','direct_typeid_noparen_declarator',4,'p_direct_typeid_noparen_declarator_6','plyparser.py',126),
  ('direct_typeid_noparen_declarator -> direct_typeid_noparen_declarator LPAREN identifier_list_opt RPAREN','direct_typeid_noparen_declarator',4,'p_direct_typeid_noparen_declarator_6','plyparser.py',127),
  ('id_declarator -> direct_id_declarator','id_declarator',1,'p_id_declarator_1','plyparser.py',126),
  ('id_declarator -> pointer direct_id_declarator','id_declarator',2,'p_id_declarator_2','plyparser.py',126),
  ('typeid_declarator -> direct_typeid_declarator','typeid_declarator',1,'p_typeid_declarator_1','plyparser.py',126),
  ('typeid_declarator -> pointer direct_typeid_declarator','typeid_declarator',2,'p_typeid_declarator_2','plyparser.py',126),
  ('typeid_noparen_declarator -> direct_typeid_noparen_declarator','typeid_noparen_declarator',1,'p_typeid_noparen_declarator_1','plyparser.py',126),
  ('typeid_noparen_declarator -> pointer direct_typeid_noparen_declarator','typeid_noparen_declarator',2,'p_typeid_noparen_declarator_2','plyparser.py',126),
  ('translation_unit_or_empty -> translation_unit','translation_unit_or_empty',1,'p_translation_unit_or_empty','c_parser.py',509),
  ('translation_unit_or_empty -> empty','translation_unit_or_empty',1,'p_translation_unit_or_empty','c_parser.py',510),
  ('translation_unit -> external_declaration','translation_unit',1,'p_translation_unit_1','c_parser.py',518),
  ('translation_unit -> translation_unit external_declaration','translation_unit',2,'p_translation_unit_2','c_parser.py',524),
  ('external_declaration -> function_definition','external_declaration',1,'p_external_declaration_1','c_parser.py',534),
  ('external_declaration -> declaration','external_declaration',1,'p_external_declaration_2','c_parser.py',539),
  ('external_declaration -> pp_directive','external_declaration',1,'p_external_declaration_3','c_parser.py',544),
  ('external_declaration -> pppragma_directive','external_declaration',1,'p_external_declaration_3','c_parser.py',545),
  ('external_declaration -> SEMI','external_declaration',1,'p_external_declaration_4','c_parser.py',550),
  ('external_declaration -> static_assert','external_declaration',1,'p_external_declaration_5','c_parser.py',555),
  ('static_assert -> _STATIC_ASSERT LPAREN constant_expression COMMA unified_string_literal RPAREN','static_assert',6,'p_static_assert_declaration','c_parser.py',560),
  ('static_assert -> _STATIC_ASSERT LPAREN constant_expression RPAREN','static_assert',4,'p_static_assert_declaration','c_parser.py',561),
  ('pp_directive -> PPHASH','pp_directive',1,'p_pp_directive','c_parser.py',569),
  ('pppragma_directive -> PPPRAGMA','pppragma_directive',1,'p_pppragma_directive','c_parser.py',580),
  ('pppragma_directive -> PPPRAGMA PPPRAGMASTR','pppragma_directive',2,'p_pppragma_directive','c_parser.py',581),
  ('pppragma_directive -> _PRAGMA LPAREN unified_string_literal RPAREN','pppragma_directive',4,'p_pppragma_directive','c_parser.py',582),
  ('pppragma_directive_list -> pppragma_directive','pppragma_directive_list',1,'p_pppragma_directive_list','c_parser.py',592),
  ('pppragma_directive_list -> pppragma_directive_list pppragma_directive','pppragma_directive_list',2,'p_pppragma_directive_list','c_parser.py',593),
  ('function_definition -> id_declarator declaration_list_opt compound_statement','function_definition',3,'p_function_definition_1','c_parser.py',600),
  ('function_definition -> declaration_specifiers id_declarator declaration_list_opt compound_statement','function_definition',4,'p_function_definition_2','c_parser.py',618),
  ('statement -> labeled_statement','statement',1,'p_statement','c_parser.py',633),
  ('statement -> expression_statement','statement',1,'p_statement','c_parser.py',634),
  ('statement -> compound_statement','statement',1,'p_statement','c_parser.py',635),
  ('statement -> selection_statement','statement',1,'p_statement','c_parser.py',636),
  ('statement -> iteration_statement','statement',1,'p_statement','c_parser.py',637),
  ('statement -> jump_statement','statement',1,'p_statement','c_parser.py',638),
  ('statement -> pppragma_directive','statement',1,'p_statement','c_parser.py',639),
  ('statement -> static_assert','statement',1,'p_statement','c_parser.py',640),
  ('pragmacomp_or_statement -> pppragma_directive_list statement','pragmacomp_or_statement',2,'p_pragmacomp_or_statement','c_parser.py',688),
  ('pragmacomp_or_statement -> statement','pragmacomp_or_statement',1,'p_pragmacomp_or_statement','c_parser.py',689),
  ('decl_body -> declaration_specifiers init_declarator_list_opt','decl_body',2,'p_decl_body','c_parser.py',708),
  ('decl_body -> declaration_specifiers_no_type id_init_declarator_list_opt','decl_body',2,'p_decl_body','c_parser.py',709),
  ('declaration -> decl_body SEMI','declaration',2,'p_declaration','c_parser.py',769),
  ('declaration_list -> declaration','declaration_list',1,'p_declaration_list','c_parser.py',778),
  ('declaration_list -> declaration_list declaration','declaration_list',2,'p_declaration_list','c_parser.py',779),
  ('declaration_specifiers_no_type -> type_qualifier declaration_specifiers_no_type_opt','declaration_specifiers_no_type',2,'p_declaration_specifiers_no_type_1','c_parser.py',789),
  ('declaration_specifiers_no_type -> storage_class_specifier declaration_specifiers_no_type_opt','declaration_specifiers_no_type',2,'p_declaration_specifiers_no_type_2','c_parser.py',794),
  ('declaration_specifiers_no_type -> function_specifier declaration_specifiers_no_type_opt','declaration_specifiers_no_type',2,'p_declaration_specifiers_no_type_3','c_parser.py',799),
  ('declaration_specifiers_no_type -> atomic_specifier declaration_specifiers_no_type_opt','declaration_specifiers_no_type',2,'p_declaration_specifiers_no_type_4','c_parser.py',806),
  ('declaration_specifiers_no_type -> alignment_specifier declaration_specifiers_no_type_opt','declaration_specifiers_no_type',2,'p_declaration_specifiers_no_type_5','c_parser.py',811),
  ('declaration_specifiers -> declaration_specifiers type_qualifier','declaration_specifiers',2,'p_declaration_specifiers_1','c_parser.py',816),
  ('declaration_specifiers -> declaration_specifiers storage_class_specifier','declaration_specifiers',2,'p_declaration_specifiers_2','c_parser.py',821),
  ('declaration_specifiers -> declaration_specifiers function_specifier','declaration_specifiers',2,'p_declaration_specifiers_3','c_parser.py',826),
  ('declaration_specifiers -> declaration_specifiers type_specifier_no_typeid','declaration_specifiers',2,'p_declaration_specifiers_4','c_parser.py',831),
  ('declaration_specifiers -> type_specifier','declaration_specifiers',1,'p_declaration_specifiers_5','c_parser.py',836),
  ('declaration_specifiers -> declaration_specifiers_no_type type_specifier','declaration_specifiers',2,'p_declaration_specifiers_6','c_parser.py',841),
  ('declaration_specifiers -> declaration_specifiers alignment_specifier','declaration_specifiers',2,'p_declaration_specifiers_7','c_parser.py',846),
  ('storage_class_specifier -> AUTO','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',851),
  ('storage_class_specifier -> REGISTER','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',852),
  ('storage_class_specifier -> STATIC','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',853),
  ('storage_class_specifier -> EXTERN','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',854),
  ('storage_class_specifier -> TYPEDEF','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',855),
  ('storage_class_specifier -> _THREAD_LOCAL','storage_class_specifier',1,'p_storage_class_specifier','c_parser.py',856),
  ('function_specifier -> INLINE','function_specifier',1,'p_function_specifier','c_parser.py',861),
  ('function_specifier -> _NORETURN','function_specifier',1,'p_function_specifier','c_parser.py',862),
  ('type_specifier_no_typeid -> VOID','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',867),
  ('type_specifier_no_typeid -> _BOOL','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',868),
  ('type_specifier_no_typeid -> CHAR','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',869),
  ('type_specifier_no_typeid -> SHORT','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',870),
  ('type_specifier_no_typeid -> INT','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',871),
  ('type_specifier_no_typeid -> LONG','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',872),
  ('type_specifier_no_typeid -> FLOAT','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',873),
  ('type_specifier_no_typeid -> DOUBLE','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',874),
  ('type_specifier_no_typeid -> _COMPLEX','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',875),
  ('type_specifier_no_typeid -> SIGNED','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',876),
  ('type_specifier_no_typeid -> UNSIGNED','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',877),
  ('type_specifier_no_typeid -> __INT128','type_specifier_no_typeid',1,'p_type_specifier_no_typeid','c_parser.py',878),
  ('type_specifier -> typedef_name','type_specifier',1,'p_type_specifier','c_parser.py',883),
  ('type_specifier -> enum_specifier','type_specifier',1,'p_type_specifier','c_parser.py',884),
  ('type_specifier -> struct_or_union_specifier','type_specifier',1,'p_type_specifier','c_parser.py',885),
  ('type_specifier -> type_specifier_no_typeid','type_specifier',1,'p_type_specifier','c_parser.py',886),
  ('type_specifier -> atomic_specifier','type_specifier',1,'p_type_specifier','c_parser.py',887),
  ('atomic_specifier -> _ATOMIC LPAREN type_name RPAREN','atomic_specifier',4,'p_atomic_specifier','c_parser.py',893),
  ('type_qualifier -> CONST','type_qualifier',1,'p_type_qualifier','c_parser.py',900),
  ('type_qualifier -> RESTRICT','type_qualifier',1,'p_type_qualifier','c_parser.py',901),
  ('type_qualifier -> VOLATILE','type_qualifier',1,'p_type_qualifier','c_parser.py',902),
  ('type_qualifier -> _ATOMIC','type_qualifier',1,'p_type_qualifier','c_parser.py',903),
  ('init_declarator_list -> init_declarator','init_declarator_list',1,'p_init_declarator_list','c_parser.py',908),
  ('init_declarator_list -> init_declarator_list COMMA init_declarator','init_declarator_list',3,'p_init_declarator_list','c_parser.py',909),
  ('init_declarator -> declarator','init_declarator',1,'p_init_declarator','c_parser.py',917),
  ('init_declarator -> declarator EQUALS initializer','init_declarator',3,'p_init_declarator','c_parser.py',918),
  ('id_init_declarator_list -> id_init_declarator','id_init_declarator_list',1,'p_id_init_declarator_list','c_parser.py',923),
  ('id_init_declarator_list -> id_init_declarator_list COMMA init_declarator','id_init_declarator_list',3,'p_id_init_declarator_list','c_parser.py',924),
  ('id_init_declarator -> id_declarator','id_init_declarator',1,'p_id_init_declarator','c_parser.py',929),
  ('id_init_declarator -> id_declarator EQUALS initializer','id_init_declarator',3,'p_id_init_declarator','c_parser.py',930),
  ('specifier_qualifier_list -> specifier_qualifier_list type_specifier_no_typeid','specifier_qualifier_list',2,'p_specifier_qualifier_list_1','c_parser.py',937),
  ('specifier_qualifier_list -> specifier_qualifier_list type_qualifier','specifier_qualifier_list',2,'p_specifier_qualifier_list_2','c_parser.py',942),
  ('specifier_qualifier_list -> type_specifier','specifier_qualifier_list',1,'p_specifier_qualifier_list_3','c_parser.py',947),
  ('specifier_qualifier_list -> type_qualifier_list type_specifier','specifier_qualifier_list',2,'p_specifier_qualifier_list_4','c_parser.py',952),
  ('specifier_qualifier_list -> alignment_specifier','specifier_qualifier_list',1,'p_specifier_qualifier_list_5','c_parser.py',957),
  ('specifier_qualifier_list -> specifier_qualifier_list alignment_specifier','specifier_qualifier_list',2,'p_specifier_qualifier_list_6','c_parser.py',962),
  ('struct_or_union_specifier -> struct_or_union ID','struct_or_union_specifier',2,'p_struct_or_union_specifier_1','c_parser.py',970),
  ('struct_or_union_specifier -> struct_or_union TYPEID','struct_or_union_specifier',2,'p_struct_or_union_specifier_1','c_parser.py',971),
  ('struct_or_union_specifier -> struct_or_union brace_open struct_declaration_list brace_close','struct_or_union_specifier',4,'p_struct_or_union_specifier_2','c_parser.py',981),
  ('struct_or_union_specifier -> struct_or_union brace_open brace_close','struct_or_union_specifier',3,'p_struct_or_union_specifier_2','c_parser.py',982),
  ('struct_or_union_specifier -> struct_or_union ID brace_open struct_declaration_list brace_close','struct_or_union_specifier',5,'p_struct_or_union_specifier_3','c_parser.py',999),
  ('struct_or_union_specifier -> struct_or_union ID brace_open brace_close','struct_or_union_specifier',4,'p_struct_or_union_specifier_3','c_parser.py',1000),
  ('struct_or_union_specifier -> struct_or_union TYPEID brace_open struct_declaration_list brace_close','struct_or_union_specifier',5,'p_struct_or_union_specifier_3','c_parser.py',1001),
  ('struct_or_union_specifier -> struct_or_union TYPEID brace_open brace_close','struct_or_union_specifier',4,'p_struct_or_union_specifier_3','c_parser.py',1002),
  ('struct_or_union -> STRUCT','struct_or_union',1,'p_struct_or_union','c_parser.py',1018),
  ('struct_or_union -> UNION','struct_or_union',1,'p_struct_or_union','c_parser.py',1019),
  ('struct_declaration_list -> struct_declaration','struct_declaration_list',1,'p_struct_declaration_list','c_parser.py',1026),
  ('struct_declaration_list -> struct_declaration_list struct_declaration','struct_declaration_list',2,'p_struct_declaration_list','c_parser.py',1027),
  ('struct_declaration -> specifier_qualifier_list struct_declarator_list_opt SEMI','struct_declaration',3,'p_struct_declaration_1','c_parser.py',1035),
  ('struct_declaration -> SEMI','struct_declaration',1,'p_struct_declaration_2','c_parser.py',1073),
  ('struct_declaration -> pppragma_directive','struct_declaration',1,'p_struct_declaration_3','c_parser.py',1078),
  ('struct_declarator_list -> struct_declarator','struct_declarator_list',1,'p_struct_declarator_list','c_parser.py',1083),
  ('struct_declarator_list -> struct_declarator_list COMMA struct_declarator','struct_declarator_list',3,'p_struct_declarator_list','c_parser.py',1084),
  ('struct_declarator -> declarator','struct_declarator',1,'p_struct_declarator_1','c_parser.py',1092),
  ('struct_declarator -> declarator COLON constant_expression','struct_declarator',3,'p_struct_declarator_2','c_parser.py',1097),
  ('struct_declarator -> COLON constant_expression','struct_declarator',2,'p_struct_declarator_2','c_parser.py',1098),
  ('enum_specifier -> ENUM ID','enum_specifier',2,'p_enum_specifier_1','c_parser.py',1106),
  ('enum_specifier -> ENUM TYPEID','enum_specifier',2,'p_enum_specifier_1','c_parser.py',1107),
  ('enum_specifier -> ENUM brace_open enumerator_list brace_close','enum_specifier',4,'p_enum_specifier_2','c_parser.py',1112),
  ('enum_specifier -> ENUM ID brace_open enumerator_list brace_close','enum_specifier',5,'p_enum_specifier_3','c_parser.py',1117),
  ('enum_specifier -> ENUM TYPEID brace_open enumerator_list brace_close','enum_specifier',5,'p_enum_specifier_3','c_parser.py',1118),
  ('enumerator_list -> enumerator','enumerator_list',1,'p_enumerator_list','c_parser.py',1123),
  ('enumerator_list -> enumerator_list COMMA','enumerator_list',2,'p_enumerator_list','c_parser.py',1124),
  ('enumerator_list -> enumerator_list COMMA enumerator','enumerator_list',3,'p_enumerator_list','c_parser.py',1125),
  ('alignment_specifier -> _ALIGNAS LPAREN type_name RPAREN','alignment_specifier',4,'p_alignment_specifier','c_parser.py',1136),
  ('alignment_specifier -> _ALIGNAS LPAREN constant_expression RPAREN','alignment_specifier',4,'p_alignment_specifier','c_parser.py',1137),
  ('enumerator -> ID','enumerator',1,'p_enumerator','c_parser.py',1142),
  ('enumerator -> ID EQUALS constant_expression','enumerator',3,'p_enumerator','c_parser.py',1143),
  ('declarator -> id_declarator','declarator',1,'p_declarator','c_parser.py',1158),
  ('declarator -> typeid_declarator','declarator',1,'p_declarator','c_parser.py',1159),
  ('pointer -> TIMES type_qualifier_list_opt','pointer',2,'p_pointer','c_parser.py',1271),
  ('pointer -> TIMES type_qualifier_list_opt pointer','pointer',3,'p_pointer','c_parser.py',1272),
  ('type_qualifier_list -> type_qualifier','type_qualifier_list',1,'p_type_qualifier_list','c_parser.py',1301),
  ('type_qualifier_list -> type_qualifier_list type_qualifier','type_qualifier_list',2,'p_type_qualifier_list','c_parser.py',1302),
  ('parameter_type_list -> parameter_list','parameter_type_list',1,'p_parameter_type_list','c_parser.py',1307),
  ('parameter_type_list -> parameter_list COMMA ELLIPSIS','parameter_type_list',3,'p_parameter_type_list','c_parser.py',1308),
  ('parameter_list -> parameter_declaration','parameter_list',1,'p_parameter_list','c_parser.py',1316),
  ('parameter_list -> parameter_list COMMA parameter_declaration','parameter_list',3,'p_parameter_list','c_parser.py',1317),
  ('parameter_declaration -> declaration_specifiers id_declarator','parameter_declaration',2,'p_parameter_declaration_1','c_parser.py',1336),
  ('parameter_declaration -> declaration_specifiers typeid_noparen_declarator','parameter_declaration',2,'p_parameter_declaration_1','c_parser.py',1337),
  ('parameter_declaration -> declaration_specifiers abstract_declarator_opt','parameter_declaration',2,'p_parameter_declaration_2','c_parser.py',1348),
  ('identifier_list -> identifier','identifier_list',1,'p_identifier_list','c_parser.py',1380),
  ('identifier_list -> identifier_list COMMA identifier','identifier_list',3,'p_identifier_list','c_parser.py',1381),
  ('initializer -> assignment_expression','initializer',1,'p_initializer_1','c_parser.py',1390),
  ('initializer -> brace_open initializer_list_opt brace_close','initializer',3,'p_initializer_2','c_parser.py',1395),
  ('initializer -> brace_open initializer_list COMMA brace_close','initializer',4,'p_initializer_2','c_parser.py',1396),
  ('initializer_list -> designation_opt initializer','initializer_list',2,'p_initializer_list','c_parser.py',1404),
  ('initializer_list -> initializer_list COMMA designation_opt initializer','initializer_list',4,'p_initializer_list','c_parser.py',1405),
  ('designation -> designator_list EQUALS','designation',2,'p_designation','c_parser.py',1416),
  ('designator_list -> designator','designator_list',1,'p_designator_list','c_parser.py',1424),
  ('designator_list -> designator_list designator','designator_list',2,'p_designator_list','c_parser.py',1425),
  ('designator -> LBRACKET constant_expression RBRACKET','designator',3,'p_designator','c_parser.py',1430),
  ('designator -> PERIOD identifier','designator',2,'p_designator','c_parser.py',1431),
  ('type_name -> specifier_qualifier_list abstract_declarator_opt','type_name',2,'p_type_name','c_parser.py',1436),
  ('abstract_declarator -> pointer','abstract_declarator',1,'p_abstract_declarator_1','c_parser.py',1448),
  ('abstract_declarator -> pointer direct_abstract_declarator','abstract_declarator',2,'p_abstract_declarator_2','c_parser.py',1456),
  ('abstract_declarator -> direct_abstract_declarator','abstract_declarator',1,'p_abstract_declarator_3','c_parser.py',1461),
  ('direct_abstract_declarator -> LPAREN abstract_declarator RPAREN','direct_abstract_declarator',3,'p_direct_abstract_declarator_1','c_parser.py',1471),
  ('direct_abstract_declarator -> direct_abstract_declarator LBRACKET assignment_expression_opt RBRACKET','direct_abstract_declarator',4,'p_direct_abstract_declarator_2','c_parser.py',1475),
  ('direct_abstract_declarator -> LBRACKET type_qualifier_list_opt assignment_expression_opt RBRACKET','direct_abstract_declarator',4,'p_direct_abstract_declarator_3','c_parser.py',1486),
  ('direct_abstract_declarator -> direct_abstract_declarator LBRACKET TIMES RBRACKET','direct_abstract_declarator',4,'p_direct_abstract_declarator_4','c_parser.py',1496),
  ('direct_abstract_declarator -> LBRACKET TIMES RBRACKET','direct_abstract_declarator',3,'p_direct_abstract_declarator_5','c_parser.py',1507),
  ('direct_abstract_declarator -> direct_abstract_declarator LPAREN parameter_type_list_opt RPAREN','direct_abstract_declarator',4,'p_direct_abstract_declarator_6','c_parser.py',1516),
  ('direct_abstract_declarator -> LPAREN parameter_type_list_opt RPAREN','direct_abstract_declarator',3,'p_direct_abstract_declarator_7','c_parser.py',1526),
  ('direct_abstract_declarator -> LBRACKET STATIC type_qualifier_list_opt assignment_expression RBRACKET','direct_abstract_declarator',5,'p_direct_abstract_declarator_8','c_parser.py',1534),
  ('direct_abstract_declarator -> LBRACKET type_qualifier_list STATIC assignment_expression RBRACKET','direct_abstract_declarator',5,'p_direct_abstract_declarator_8','c_parser.py',1535),
  ('block_item -> declaration','block_item',1,'p_block_item','c_parser.py',1551),
  ('block_item -> statement','block_item',1,'p_block_item','c_parser.py',1552),
  ('block_item_list -> block_item','block_item_list',1,'p_block_item_list','c_parser.py',1559),
  ('block_item_list -> block_item_list block_item','block_item_list',2,'p_block_item_list','c_parser.py',1560),
  ('compound_statement -> brace_open block_item_list_opt brace_close','compound_statement',3,'p_compound_statement_1','c_parser.py',1566),
  ('labeled_statement -> ID COLON pragmacomp_or_statement','labeled_statement',3,'p_labeled_statement_1','c_parser.py',1572),
  ('labeled_statement -> CASE constant_expression COLON pragmacomp_or_statement','labeled_statement',4,'p_labeled_statement_2','c_parser.py',1576),
  ('labeled_statement -> DEFAULT COLON pragmacomp_or_statement','labeled_statement',3,'p_labeled_statement_3','c_parser.py',1580),
  ('labeled_statement -> ID COLON','labeled_statement',2,'p_labeled_statement_4','c_parser.py',1584),
  ('labeled_statement -> CASE constant_expression COLON','labeled_statement',3,'p_labeled_statement_5','c_parser.py',1588),
  ('labeled_statement -> DEFAULT COLON','labeled_statement',2,'p_labeled_statement_6','c_parser.py',1592),
  ('selection_statement -> IF LPAREN expression RPAREN pragmacomp_or_statement','selection_statement',5,'p_selection_statement_1','c_parser.py',1596),
  ('selection_statement -> IF LPAREN expression RPAREN statement ELSE pragmacomp_or_statement','selection_statement',7,'p_selection_statement_2','c_parser.py',1600),
  ('selection_statement -> SWITCH LPAREN expression RPAREN pragmacomp_or_statement','selection_statement',5,'p_selection_statement_3','c_parser.py',1604),
  ('iteration_statement -> WHILE LPAREN expression RPAREN pragmacomp_or_statement','iteration_statement',5,'p_iteration_statement_1','c_parser.py',1609),
  ('iteration_statement -> DO pragmacomp_or_statement WHILE LPAREN expression RPAREN SEMI','iteration_statement',7,'p_iteration_statement_2','c_parser.py',1613),
  ('iteration_statement -> FOR LPAREN expression_opt SEMI expression_opt SEMI expression_opt RPAREN pragmacomp_or_statement','iteration_statement',9,'p_iteration_statement_3','c_parser.py',1617),
  ('iteration_statement -> FOR LPAREN declaration expression_opt SEMI expression_opt RPAREN pragmacomp_or_statement','iteration_statement',8,'p_iteration_statement_4','c_parser.py',1621),
  ('jump_statement -> GOTO ID SEMI','jump_statement',3,'p_jump_statement_1','c_parser.py',1626),
  ('jump_statement -> BREAK SEMI','jump_statement',2,'p_jump_statement_2','c_parser.py',1630),
  ('jump_statement -> CONTINUE SEMI','jump_statement',2,'p_jump_statement_3','c_parser.py',1634),
  ('jump_statement -> RETURN expression SEMI','jump_statement',3,'p_jump_statement_4','c_parser.py',1638),
  ('jump_statement -> RETURN SEMI','jump_statement',2,'p_jump_statement_4','c_parser.py',1639),
  ('expression_statement -> expression_opt SEMI','expression_statement',2,'p_expression_statement','c_parser.py',1644),
  ('expression -> assignment_expression','expression',1,'p_expression','c_parser.py',1651),
  ('expression -> expression COMMA assignment_expression','expression',3,'p_expression','c_parser.py',1652),
  ('assignment_expression -> LPAREN compound_statement RPAREN','assignment_expression',3,'p_parenthesized_compound_expression','c_parser.py',1664),
  ('typedef_name -> TYPEID','typedef_name',1,'p_typedef_name','c_parser.py',1668),
  ('assignment_expression -> conditional_expression','assignment_expression',1,'p_assignment_expression','c_parser.py',1672),
  ('assignment_expression -> unary_expression assignment_operator assignment_expression','assignment_expression',3,'p_assignment_expression','c_parser.py',1673),
  ('assignment_operator -> EQUALS','assignment_operator',1,'p_assignment_operator','c_parser.py',1686),
  ('assignment_operator -> XOREQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1687),
  ('assignment_operator -> TIMESEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1688),
  ('assignment_operator -> DIVEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1689),
  ('assignment_operator -> MODEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1690),
  ('assignment_operator -> PLUSEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1691),
  ('assignment_operator -> MINUSEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1692),
  ('assignment_operator -> LSHIFTEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1693),
  ('assignment_operator -> RSHIFTEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1694),
  ('assignment_operator -> ANDEQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1695),
  ('assignment_operator -> OREQUAL','assignment_operator',1,'p_assignment_operator','c_parser.py',1696),
  ('constant_expression -> conditional_expression','constant_expression',1,'p_constant_expression','c_parser.py',1701),
  ('conditional_expression -> binary_expression','conditional_expression',1,'p_conditional_expression','c_parser.py',1705),
  ('conditional_expression -> binary_expression CONDOP expression COLON conditional_expression','conditional_expression',5,'p_conditional_expression','c_parser.py',1706),
  ('binary_expression -> cast_expression','binary_expression',1,'p_binary_expression','c_parser.py',1714),
  ('binary_expression -> binary_expression TIMES binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1715),
  ('binary_expression -> binary_expression DIVIDE binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1716),
  ('binary_expression -> binary_expression MOD binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1717),
  ('binary_expression -> binary_expression PLUS binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1718),
  ('binary_expression -> binary_expression MINUS binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1719),
  ('binary_expression -> binary_expression RSHIFT binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1720),
  ('binary_expression -> binary_expression LSHIFT binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1721),
  ('binary_expression -> binary_expression LT binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1722),
  ('binary_expression -> binary_expression LE binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1723),
  ('binary_expression -> binary_expression GE binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1724),
  ('binary_expression -> binary_expression GT binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1725),
  ('binary_expression -> binary_expression EQ binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1726),
  ('binary_expression -> binary_expression NE binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1727),
  ('binary_expression -> binary_expression AND binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1728),
  ('binary_expression -> binary_expression OR binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1729),
  ('binary_expression -> binary_expression XOR binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1730),
  ('binary_expression -> binary_expression LAND binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1731),
  ('binary_expression -> binary_expression LOR binary_expression','binary_expression',3,'p_binary_expression','c_parser.py',1732),
  ('cast_expression -> unary_expression','cast_expression',1,'p_cast_expression_1','c_parser.py',1740),
  ('cast_expression -> LPAREN type_name RPAREN cast_expression','cast_expression',4,'p_cast_expression_2','c_parser.py',1744),
  ('unary_expression -> postfix_expression','unary_expression',1,'p_unary_expression_1','c_parser.py',1748),
  ('unary_expression -> PLUSPLUS unary_expression','unary_expression',2,'p_unary_expression_2','c_parser.py',1752),
  ('unary_expression -> MINUSMINUS unary_expression','unary_expression',2,'p_unary_expression_2','c_parser.py',1753),
  ('unary_expression -> unary_operator cast_expression','unary_expression',2,'p_unary_expression_2','c_parser.py',1754),
  ('unary_expression -> SIZEOF unary_expression','unary_expression',2,'p_unary_expression_3','c_parser.py',1759),
  ('unary_expression -> SIZEOF LPAREN type_name RPAREN','unary_expression',4,'p_unary_expression_3','c_parser.py',1760),
  ('unary_expression -> _ALIGNOF LPAREN type_name RPAREN','unary_expression',4,'p_unary_expression_3','c_parser.py',1761),
  ('unary_operator -> AND','unary_operator',1,'p_unary_operator','c_parser.py',1769),
  ('unary_operator -> TIMES','unary_operator',1,'p_unary_operator','c_parser.py',1770),
  ('unary_operator -> PLUS','unary_operator',1,'p_unary_operator','c_parser.py',1771),
  ('unary_operator -> MINUS','unary_operator',1,'p_unary_operator','c_parser.py',1772),
  ('unary_operator -> NOT','unary_operator',1,'p_unary_operator','c_parser.py',1773),
  ('unary_operator -> LNOT','unary_operator',1,'p_unary_operator','c_parser.py',1774),
  ('postfix_expression -> primary_expression','postfix_expression',1,'p_postfix_expression_1','c_parser.py',1779),
  ('postfix_expression -> postfix_expression LBRACKET expression RBRACKET','postfix_expression',4,'p_postfix_expression_2','c_parser.py',1783),
  ('postfix_expression -> postfix_expression LPAREN argument_expression_list RPAREN','postfix_expression',4,'p_postfix_expression_3','c_parser.py',1787),
  ('postfix_expression -> postfix_expression LPAREN RPAREN','postfix_expression',3,'p_postfix_expression_3','c_parser.py',1788),
  ('postfix_expression -> postfix_expression PERIOD ID','postfix_expression',3,'p_postfix_expression_4','c_parser.py',1793),
  ('postfix_expression -> postfix_expression PERIOD TYPEID','postfix_expression',3,'p_postfix_expression_4','c_parser.py',1794),
  ('postfix_expression -> postfix_expression ARROW ID','postfix_expression',3,'p_postfix_expression_4','c_parser.py',1795),
  ('postfix_expression -> postfix_expression ARROW TYPEID','postfix_expression',3,'p_postfix_expression_4','c_parser.py',1796),
  ('postfix_expression -> postfix_expression PLUSPLUS','postfix_expression',2,'p_postfix_expression_5','c_parser.py',1802),
  ('postfix_expression -> postfix_expression MINUSMINUS','postfix_expression',2,'p_postfix_expression_5','c_parser.py',1803),
  ('postfix_expression -> LPAREN type_name RPAREN brace_open initializer_list brace_close','postfix_expression',6,'p_postfix_expression_6','c_parser.py',1808),
  ('postfix_expression -> LPAREN type_name RPAREN brace_open initializer_list COMMA brace_close','postfix_expression',7,'p_postfix_expression_6','c_parser.py',1809),
  ('primary_expression -> identifier','primary_expression',1,'p_primary_expression_1','c_parser.py',1814),
  ('primary_expression -> constant','primary_expression',1,'p_primary_expression_2','c_parser.py',1818),
  ('primary_expression -> unified_string_literal','primary_expression',1,'p_primary_expression_3','c_parser.py',1822),
  ('primary_expression -> unified_wstring_literal','primary_expression',1,'p_primary_expression_3','c_parser.py',1823),
  ('primary_expression -> LPAREN expression RPAREN','primary_expression',3,'p_primary_expression_4','c_parser.py',1828),
  ('primary_expression -> OFFSETOF LPAREN type_name COMMA offsetof_member_designator RPAREN','primary_expression',6,'p_primary_expression_5','c_parser.py',1832),
  ('offsetof_member_designator -> identifier','offsetof_member_designator',1,'p_offsetof_member_designator','c_parser.py',1840),
  ('offsetof_member_designator -> offsetof_member_designator PERIOD identifier','offsetof_member_designator',3,'p_offsetof_member_designator','c_parser.py',1841),
  ('offsetof_member_designator -> offsetof_member_designator LBRACKET expression RBRACKET','offsetof_member_designator',4,'p_offsetof_member_designator','c_parser.py',1842),
  ('argument_expression_list -> assignment_expression','argument_expression_list',1,'p_argument_expression_list','c_parser.py',1854),
  ('argument_expression_list -> argument_expression_list COMMA assignment_expression','argument_expression_list',3,'p_argument_expression_list','c_parser.py',1855),
  ('identifier -> ID','identifier',1,'p_identifier','c_parser.py',1864),
  ('constant -> INT_CONST_DEC','constant',1,'p_constant_1','c_parser.py',1868),
  ('constant -> INT_CONST_OCT','constant',1,'p_constant_1','c_parser.py',1869),
  ('constant -> INT_CONST_HEX','constant',1,'p_constant_1','c_parser.py',1870),
  ('constant -> INT_CONST_BIN','constant',1,'p_constant_1','c_parser.py',1871),
  ('constant -> INT_CONST_CHAR','constant',1,'p_constant_1','c_parser.py',1872),
  ('constant -> FLOAT_CONST','constant',1,'p_constant_2','c_parser.py',1891),
  ('constant -> HEX_FLOAT_CONST','constant',1,'p_constant_2','c_parser.py',1892),
  ('constant -> CHAR_CONST','constant',1,'p_constant_3','c_parser.py',1905),
  ('constant -> WCHAR_CONST','constant',1,'p_constant_3','c_parser.py',1906),
  ('constant -> U8CHAR_CONST','constant',1,'p_constant_3','c_parser.py',1907),
  ('constant -> U16CHAR_CONST','constant',1,'p_constant_3','c_parser.py',1908),
  ('constant -> U32CHAR_CONST','constant',1,'p_constant_3','c_parser.py',1909),
  ('unified_string_literal -> STRING_LITERAL','unified_string_literal',1,'p_unified_string_literal','c_parser.py',1920),
  ('unified_string_literal -> unified_string_literal STRING_LITERAL','unified_string_literal',2,'p_unified_string_literal','c_parser.py',1921),
  ('unified_wstring_literal -> WSTRING_LITERAL','unified_wstring_literal',1,'p_unified_wstring_literal','c_parser.py',1931),
  ('unified_wstring_literal -> U8STRING_LITERAL','unified_wstring_literal',1,'p_unified_wstring_literal','c_parser.py',1932),
  ('unified_wstring_literal -> U16STRING_LITERAL','unified_wstring_literal',1,'p_unified_wstring_literal','c_parser.py',1933),
  ('unified_wstring_literal -> U32STRING_LITERAL','unified_wstring_literal',1,'p_unified_wstring_literal','c_parser.py',1934),
  ('unified_wstring_literal -> unified_wstring_literal WSTRING_LITERAL','unified_wstring_literal',2,'p_unified_wstring_literal','c_parser.py',1935),
  ('unified_wstring_literal -> unified_wstring_literal U8STRING_LITERAL','unified_wstring_literal',2,'p_unified_wstring_literal','c_parser.py',1936),
  ('unified_wstring_literal -> unified_wstring_literal U16STRING_LITERAL','unified_wstring_literal',2,'p_unified_wstring_literal','c_parser.py',1937),
  ('unified_wstring_literal -> unified_wstring_literal U32STRING_LITERAL','unified_wstring_literal',2,'p_unified_wstring_literal','c_parser.py',1938),
  ('brace_open -> LBRACE','brace_open',1,'p_brace_open','c_parser.py',1948),
  ('brace_close -> RBRACE','brace_close',1,'p_brace_close','c_parser.py',1954),
  ('empty -> <empty>','empty',0,'p_empty','c_parser.py',1960),
]
