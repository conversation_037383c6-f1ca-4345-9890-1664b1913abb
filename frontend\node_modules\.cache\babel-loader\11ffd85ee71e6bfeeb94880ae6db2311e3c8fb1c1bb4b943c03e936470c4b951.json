{"ast": null, "code": "import api from './api';\nexport const login = async credentials => {\n  const response = await api.post('/auth/login', credentials);\n  if (response.data.access_token) {\n    localStorage.setItem('token', response.data.access_token);\n  }\n  return response.data;\n};\nexport const register = async userData => {\n  const response = await api.post('/auth/register', userData);\n  if (response.data.access_token) {\n    localStorage.setItem('token', response.data.access_token);\n  }\n  return response.data;\n};\nexport const getMe = async () => {\n  const response = await api.get('/auth/me');\n  return response.data;\n};\nexport const logout = () => {\n  localStorage.removeItem('token');\n};", "map": {"version": 3, "names": ["api", "login", "credentials", "response", "post", "data", "access_token", "localStorage", "setItem", "register", "userData", "getMe", "get", "logout", "removeItem"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/services/authService.ts"], "sourcesContent": ["import api from './api';\nimport { User, LoginCredentials, RegisterData } from '../types';\n\ninterface AuthResponse {\n  access_token: string;\n  message?: string;\n}\n\nexport const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {\n  const response = await api.post('/auth/login', credentials);\n  if (response.data.access_token) {\n    localStorage.setItem('token', response.data.access_token);\n  }\n  return response.data;\n};\n\nexport const register = async (userData: RegisterData): Promise<AuthResponse> => {\n  const response = await api.post('/auth/register', userData);\n  if (response.data.access_token) {\n    localStorage.setItem('token', response.data.access_token);\n  }\n  return response.data;\n};\n\nexport const getMe = async (): Promise<User> => {\n  const response = await api.get('/auth/me');\n  return response.data;\n};\n\nexport const logout = () => {\n  localStorage.removeItem('token');\n};"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAQvB,OAAO,MAAMC,KAAK,GAAG,MAAOC,WAA6B,IAA4B;EACnF,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,aAAa,EAAEF,WAAW,CAAC;EAC3D,IAAIC,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;IAC9BC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACC,YAAY,CAAC;EAC3D;EACA,OAAOH,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAG,MAAOC,QAAsB,IAA4B;EAC/E,MAAMP,QAAQ,GAAG,MAAMH,GAAG,CAACI,IAAI,CAAC,gBAAgB,EAAEM,QAAQ,CAAC;EAC3D,IAAIP,QAAQ,CAACE,IAAI,CAACC,YAAY,EAAE;IAC9BC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACE,IAAI,CAACC,YAAY,CAAC;EAC3D;EACA,OAAOH,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMM,KAAK,GAAG,MAAAA,CAAA,KAA2B;EAC9C,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACY,GAAG,CAAC,UAAU,CAAC;EAC1C,OAAOT,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMQ,MAAM,GAAGA,CAAA,KAAM;EAC1BN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}