Write-Host "=== Démarrage de l'application de gestion des dépenses ===" -ForegroundColor Green

# Vérifier si Python est installé
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python détecté: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Erreur: Python n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    exit 1
}

# Vérifier si Node.js est installé
try {
    $nodeVersion = node --version 2>&1
    Write-Host "Node.js détecté: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Erreur: Node.js n'est pas installé ou pas dans le PATH" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Initialisation de la base de données ===" -ForegroundColor Yellow
Set-Location backend
python init_db.py

if ($LASTEXITCODE -ne 0) {
    Write-Host "Erreur lors de l'initialisation de la base de données" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Démarrage du serveur backend (Flask) ===" -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; python run.py"

Write-Host "`n=== Démarrage du serveur frontend (React) ===" -ForegroundColor Yellow
Set-Location ..\frontend

# Vérifier si node_modules existe
if (!(Test-Path "node_modules")) {
    Write-Host "Installation des dépendances npm..." -ForegroundColor Yellow
    npm install
}

Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; npm start"

Write-Host "`n=== Serveurs démarrés ===" -ForegroundColor Green
Write-Host "Backend: http://127.0.0.1:5000" -ForegroundColor Cyan
Write-Host "Frontend: http://localhost:3006" -ForegroundColor Cyan
Write-Host "`nAttendez quelques secondes que les serveurs se lancent..." -ForegroundColor Yellow

# Attendre 5 secondes puis ouvrir le navigateur
Start-Sleep -Seconds 5
Start-Process "http://localhost:3006"

Write-Host "`nPour arrêter les serveurs, fermez les fenêtres PowerShell ouvertes." -ForegroundColor Yellow
Write-Host "Appuyez sur une touche pour continuer..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
