#!/usr/bin/env python3
"""
Script pour installer les dépendances manquantes
"""
import subprocess
import sys

def install_package(package):
    """Installer un package avec pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} installé avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Erreur lors de l'installation de {package}: {e}")
        return False

def main():
    print("Installation des dépendances manquantes...")
    
    # Packages essentiels manquants
    packages = [
        "Flask-CORS==4.0.0",
        "Flask-JWT-Extended==4.6.0", 
        "Flask-Admin==1.6.1"
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n{success_count}/{len(packages)} packages installés avec succès")
    
    if success_count == len(packages):
        print("✓ Toutes les dépendances sont installées!")
        print("Vous pouvez maintenant démarrer le serveur avec: python run.py")
    else:
        print("✗ Certaines dépendances n'ont pas pu être installées")
        print("Essayez d'installer manuellement avec: pip install -r requirements.txt")

if __name__ == '__main__':
    main()
