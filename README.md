# Application de Gestion des Dépenses

Une application web complète pour gérer vos dépenses avec un backend Flask et un frontend React.

## Architecture

- **Backend**: Flask avec SQLAlchemy, JWT pour l'authentification
- **Frontend**: React avec TypeScript et Tailwind CSS
- **Base de données**: SQLite (par défaut)

## Ports

- Backend: http://127.0.0.1:5000
- Frontend: http://localhost:3006

## Installation et Démarrage Rapide

### Option 1: Script automatique (Windows)
```bash
# Exécuter le script de démarrage
start_servers.bat
```

### Option 2: Démarrage manuel

#### Backend
```bash
cd backend

# Installer les dépendances
pip install -r requirements.txt

# Initialiser la base de données
python init_db.py

# Démarrer le serveur
python run.py
```

#### Frontend
```bash
cd frontend

# Installer les dépendances
npm install

# Démarrer le serveur de développement
npm start
```

## Test de l'API

Pour tester que l'API fonctionne correctement :

```bash
cd backend
python test_api.py
```

## Fonctionnalités

### Authentification
- Inscription d'utilisateur
- Connexion avec JWT
- Protection des routes

### Gestion des Catégories
- Créer, lire, modifier, supprimer des catégories
- Catégories liées à l'utilisateur connecté

### Gestion des Sous-catégories
- CRUD complet pour les sous-catégories
- Liées aux catégories de l'utilisateur

### Gestion des Dépenses
- Enregistrement des dépenses avec montant, description, date
- Liées aux sous-catégories
- Historique complet des dépenses

### Dashboard
- Vue d'ensemble des dépenses
- Statistiques et graphiques

## Structure des API

### Authentification
- `POST /api/auth/register` - Inscription
- `POST /api/auth/login` - Connexion
- `GET /api/auth/me` - Profil utilisateur

### Catégories
- `GET /api/categories` - Liste des catégories
- `POST /api/categories` - Créer une catégorie
- `PUT /api/categories/{id}` - Modifier une catégorie
- `DELETE /api/categories/{id}` - Supprimer une catégorie

### Sous-catégories
- `GET /api/subcategories` - Liste des sous-catégories
- `POST /api/subcategories` - Créer une sous-catégorie
- `PUT /api/subcategories/{id}` - Modifier une sous-catégorie
- `DELETE /api/subcategories/{id}` - Supprimer une sous-catégorie

### Dépenses
- `GET /api/expenses` - Liste des dépenses
- `POST /api/expenses` - Créer une dépense
- `PUT /api/expenses/{id}` - Modifier une dépense
- `DELETE /api/expenses/{id}` - Supprimer une dépense

## Résolution des Problèmes

### Erreur "Internal Server Error"
1. Vérifiez que la base de données est initialisée : `python init_db.py`
2. Vérifiez les logs du serveur Flask
3. Assurez-vous que les ports 5000 et 3006 sont libres

### Problèmes de CORS
- La configuration CORS est définie pour accepter les requêtes du frontend
- Vérifiez que les URLs correspondent dans les configurations

### Problèmes d'authentification
- Vérifiez que le token JWT est correctement stocké dans localStorage
- Testez l'API avec le script de test : `python test_api.py`

## Utilisateur de Test

Un utilisateur de test est créé automatiquement :
- Username: `admin`
- Password: `admin123`
- Email: `<EMAIL>`
