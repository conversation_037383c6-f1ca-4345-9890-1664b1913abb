{"ast": null, "code": "import api from './api';\nexport const getCategories = async () => {\n  const response = await api.get('/categories');\n  return response.data;\n};\nexport const createCategory = async categoryData => {\n  const response = await api.post('/categories', categoryData);\n  return response.data.category;\n};\nexport const updateCategory = async (id, categoryData) => {\n  const response = await api.put(`/categories/${id}`, categoryData);\n  return response.data.category;\n};\nexport const deleteCategory = async id => {\n  await api.delete(`/categories/${id}`);\n};\nexport const getCategory = async id => {\n  const response = await api.get(`/categories/${id}`);\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "getCategories", "response", "get", "data", "createCategory", "categoryData", "post", "category", "updateCategory", "id", "put", "deleteCategory", "delete", "getCategory"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/services/categoryService.ts"], "sourcesContent": ["import api from './api';\nimport { Category } from '../types';\n\nexport const getCategories = async (): Promise<Category[]> => {\n  const response = await api.get('/categories');\n  return response.data;\n};\n\nexport const createCategory = async (categoryData: { name: string }): Promise<Category> => {\n  const response = await api.post('/categories', categoryData);\n  return response.data.category;\n};\n\nexport const updateCategory = async (id: string, categoryData: { name: string }): Promise<Category> => {\n  const response = await api.put(`/categories/${id}`, categoryData);\n  return response.data.category;\n};\n\nexport const deleteCategory = async (id: string): Promise<void> => {\n  await api.delete(`/categories/${id}`);\n};\n\nexport const getCategory = async (id: string): Promise<Category> => {\n  const response = await api.get(`/categories/${id}`);\n  return response.data;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAGvB,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAiC;EAC5D,MAAMC,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,aAAa,CAAC;EAC7C,OAAOD,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAOC,YAA8B,IAAwB;EACzF,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACO,IAAI,CAAC,aAAa,EAAED,YAAY,CAAC;EAC5D,OAAOJ,QAAQ,CAACE,IAAI,CAACI,QAAQ;AAC/B,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAOC,EAAU,EAAEJ,YAA8B,KAAwB;EACrG,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACW,GAAG,CAAC,eAAeD,EAAE,EAAE,EAAEJ,YAAY,CAAC;EACjE,OAAOJ,QAAQ,CAACE,IAAI,CAACI,QAAQ;AAC/B,CAAC;AAED,OAAO,MAAMI,cAAc,GAAG,MAAOF,EAAU,IAAoB;EACjE,MAAMV,GAAG,CAACa,MAAM,CAAC,eAAeH,EAAE,EAAE,CAAC;AACvC,CAAC;AAED,OAAO,MAAMI,WAAW,GAAG,MAAOJ,EAAU,IAAwB;EAClE,MAAMR,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,eAAeO,EAAE,EAAE,CAAC;EACnD,OAAOR,QAAQ,CAACE,IAAI;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}