#!/usr/bin/env python3
"""
Script pour installer les dépendances et tester l'application
"""
import subprocess
import sys
import os

def install_dependencies():
    """Installer les dépendances"""
    print("=== Installation des dépendances ===")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Dépendances installées")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Erreur installation: {e}")
        return False

def init_database():
    """Initialiser la base de données"""
    print("\n=== Initialisation de la base de données ===")
    try:
        from app import create_app, db
        from app.models.models import User
        
        app = create_app()
        with app.app_context():
            # Créer les tables
            db.create_all()
            print("✓ Tables créées")
            
            # Créer un utilisateur de test
            existing_user = User.query.filter_by(username='admin').first()
            if not existing_user:
                admin_user = User(username='admin', email='<EMAIL>')
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✓ Utilisateur admin créé (admin/admin123)")
            else:
                print("✓ Utilisateur admin existe déjà")
                
        return True
    except Exception as e:
        print(f"✗ Erreur base de données: {e}")
        return False

def test_jwt():
    """Tester JWT"""
    print("\n=== Test JWT ===")
    try:
        from flask_jwt_extended import create_access_token
        from app import create_app
        
        app = create_app()
        with app.app_context():
            token = create_access_token(identity=1)
            print(f"✓ Token JWT créé: {token[:30]}...")
        return True
    except Exception as e:
        print(f"✗ Erreur JWT: {e}")
        return False

def main():
    print("=== Configuration de l'application ===\n")
    
    # Installation des dépendances
    if not install_dependencies():
        print("❌ Échec de l'installation")
        return
    
    # Test JWT
    if not test_jwt():
        print("❌ Problème avec JWT")
        return
        
    # Initialisation de la base de données
    if not init_database():
        print("❌ Problème avec la base de données")
        return
    
    print("\n✅ Configuration terminée avec succès!")
    print("\nVous pouvez maintenant:")
    print("1. Démarrer le serveur: python run.py")
    print("2. Tester l'API: python test_jwt_detailed.py")
    print("3. Utiliser le frontend sur http://localhost:3006")
    print("\nUtilisateur de test: admin / admin123")

if __name__ == '__main__':
    main()
