from flask import Blueprint, request, jsonify
from app import db
from app.models.models import Category
from flask_jwt_extended import jwt_required, get_jwt_identity

bp = Blueprint('categories', __name__)

@bp.route('', methods=['POST'])
@jwt_required()
def create_category():
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    if not data or not 'name' in data:
        return jsonify({'message': 'Missing category name'}), 400

    category = Category(name=data['name'], user_id=current_user_id)
    db.session.add(category)
    db.session.commit()

    return jsonify({'message': 'Category created', 'category': {'id': category.id, 'name': category.name}}), 201

@bp.route('', methods=['GET'])
@jwt_required()
def get_categories():
    current_user_id = int(get_jwt_identity())
    categories = Category.query.filter_by(user_id=current_user_id).all()
    return jsonify([{'id': c.id, 'name': c.name} for c in categories])

@bp.route('/<int:id>', methods=['GET'])
@jwt_required()
def get_category(id):
    current_user_id = int(get_jwt_identity())
    category = Category.query.filter_by(id=id, user_id=current_user_id).first_or_404()
    return jsonify({'id': category.id, 'name': category.name})

@bp.route('/<int:id>', methods=['PUT'])
@jwt_required()
def update_category(id):
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    if not data or not 'name' in data:
        return jsonify({'message': 'Missing category name'}), 400

    category = Category.query.filter_by(id=id, user_id=current_user_id).first_or_404()
    category.name = data['name']
    db.session.commit()

    return jsonify({'message': 'Category updated', 'category': {'id': category.id, 'name': category.name}})

@bp.route('/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_category(id):
    current_user_id = int(get_jwt_identity())
    category = Category.query.filter_by(id=id, user_id=current_user_id).first_or_404()

    db.session.delete(category)
    db.session.commit()

    return jsonify({'message': 'Category deleted'})
