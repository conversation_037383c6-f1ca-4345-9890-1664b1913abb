{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\Simple_1\\\\Simple_flask_react\\\\frontend\\\\src\\\\pages\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { login } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState(null);\n  const navigate = useNavigate();\n  const {\n    login: authLogin\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError(null);\n    try {\n      // Envoie username au lieu de email\n      const response = await login({\n        username,\n        password\n      });\n      authLogin(response.access_token);\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Login failed');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-900\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"p-8 bg-gray-800 rounded-lg shadow-md w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-white mb-6 text-center\",\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-500 text-center mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-gray-300 text-sm font-bold mb-2\",\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"username\",\n          className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\",\n          value: username,\n          onChange: e => setUsername(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-gray-300 text-sm font-bold mb-2\",\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          id: \"password\",\n          className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\",\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/register\",\n          className: \"inline-block align-baseline font-bold text-sm text-blue-400 hover:text-blue-600\",\n          children: \"Don't have an account? Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"/yVNNwWRO5p8F+jUinZrKmkDOt0=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "login", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "username", "setUsername", "password", "setPassword", "error", "setError", "navigate", "auth<PERSON><PERSON><PERSON>", "handleSubmit", "e", "preventDefault", "response", "access_token", "err", "_err$response", "_err$response$data", "data", "message", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "value", "onChange", "target", "required", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/pages/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { login } from '../services/authService';\n\nconst LoginPage: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const navigate = useNavigate();\n  const { login: authLogin } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n    try {\n      // Envoie username au lieu de email\n      const response = await login({ username, password });\n      authLogin(response.access_token);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Login failed');\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"p-8 bg-gray-800 rounded-lg shadow-md w-full max-w-md\">\n        <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">Login</h2>\n        {error && <p className=\"text-red-500 text-center mb-4\">{error}</p>}\n        <div className=\"mb-4\">\n          <label className=\"block text-gray-300 text-sm font-bold mb-2\" htmlFor=\"username\">Username</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\"\n            value={username}\n            onChange={(e) => setUsername(e.target.value)}\n            required\n          />\n        </div>\n        <div className=\"mb-6\">\n          <label className=\"block text-gray-300 text-sm font-bold mb-2\" htmlFor=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\"\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            required\n          />\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <button\n            type=\"submit\"\n            className=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n          >\n            Sign In\n          </button>\n          <a href=\"/register\" className=\"inline-block align-baseline font-bold text-sm text-blue-400 hover:text-blue-600\">\n            Don't have an account? Register\n          </a>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default LoginPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,KAAK,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEE,KAAK,EAAEY;EAAU,CAAC,GAAGb,OAAO,CAAC,CAAC;EAEtC,MAAMc,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBL,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF;MACA,MAAMM,QAAQ,GAAG,MAAMhB,KAAK,CAAC;QAAEK,QAAQ;QAAEE;MAAS,CAAC,CAAC;MACpDK,SAAS,CAACI,QAAQ,CAACC,YAAY,CAAC;MAChCN,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOO,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBV,QAAQ,CAAC,EAAAS,aAAA,GAAAD,GAAG,CAACF,QAAQ,cAAAG,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBE,OAAO,KAAI,cAAc,CAAC;IACzD;EACF,CAAC;EAED,oBACEpB,OAAA;IAAKqB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEtB,OAAA;MAAMuB,QAAQ,EAAEZ,YAAa;MAACU,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAC5FtB,OAAA;QAAIqB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAAK;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACxEpB,KAAK,iBAAIP,OAAA;QAAGqB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAEf;MAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClE3B,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBtB,OAAA;UAAOqB,SAAS,EAAC,4CAA4C;UAACO,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjG3B,OAAA;UACE6B,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACbT,SAAS,EAAC,mKAAmK;UAC7KU,KAAK,EAAE5B,QAAS;UAChB6B,QAAQ,EAAGpB,CAAC,IAAKR,WAAW,CAACQ,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;UAC7CG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKqB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBtB,OAAA;UAAOqB,SAAS,EAAC,4CAA4C;UAACO,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjG3B,OAAA;UACE6B,IAAI,EAAC,UAAU;UACfC,EAAE,EAAC,UAAU;UACbT,SAAS,EAAC,wKAAwK;UAClLU,KAAK,EAAE1B,QAAS;UAChB2B,QAAQ,EAAGpB,CAAC,IAAKN,WAAW,CAACM,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;UAC7CG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3B,OAAA;QAAKqB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDtB,OAAA;UACE6B,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,8GAA8G;UAAAC,QAAA,EACzH;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3B,OAAA;UAAGmC,IAAI,EAAC,WAAW;UAACd,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAAC;QAEhH;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzB,EAAA,CA7DID,SAAmB;EAAA,QAINL,WAAW,EACCC,OAAO;AAAA;AAAAuC,EAAA,GALhCnC,SAAmB;AA+DzB,eAAeA,SAAS;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}