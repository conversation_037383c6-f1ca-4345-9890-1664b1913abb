[{"c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx": "1", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts": "2", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx": "3", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx": "4", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx": "5", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx": "6", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx": "7", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx": "8", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx": "9", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx": "10", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx": "11", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx": "12", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts": "13", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts": "14", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\categoryService.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\subcategoryService.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\expenseService.ts": "33"}, {"size": 554, "mtime": 1757964364690, "results": "34", "hashOfConfig": "35"}, {"size": 424, "mtime": 1757964369041, "results": "36", "hashOfConfig": "35"}, {"size": 1426, "mtime": 1757964357013, "results": "37", "hashOfConfig": "35"}, {"size": 1833, "mtime": 1757964389487, "results": "38", "hashOfConfig": "35"}, {"size": 896, "mtime": 1757964416455, "results": "39", "hashOfConfig": "35"}, {"size": 541, "mtime": 1757964403155, "results": "40", "hashOfConfig": "35"}, {"size": 856, "mtime": 1757964419273, "results": "41", "hashOfConfig": "35"}, {"size": 882, "mtime": 1757964422192, "results": "42", "hashOfConfig": "35"}, {"size": 2726, "mtime": 1757964394641, "results": "43", "hashOfConfig": "35"}, {"size": 3333, "mtime": 1757964399652, "results": "44", "hashOfConfig": "35"}, {"size": 1501, "mtime": 1757964407089, "results": "45", "hashOfConfig": "35"}, {"size": 612, "mtime": 1757964425447, "results": "46", "hashOfConfig": "35"}, {"size": 854, "mtime": 1757964385286, "results": "47", "hashOfConfig": "35"}, {"size": 492, "mtime": 1757964382357, "results": "48", "hashOfConfig": "35"}, {"size": 1232, "mtime": 1757964410759, "results": "49", "hashOfConfig": "35"}, {"size": 554, "mtime": 1757964364690, "results": "50", "hashOfConfig": "51"}, {"size": 424, "mtime": 1757964369041, "results": "52", "hashOfConfig": "51"}, {"size": 1426, "mtime": 1757964357013, "results": "53", "hashOfConfig": "51"}, {"size": 541, "mtime": 1757964403155, "results": "54", "hashOfConfig": "51"}, {"size": 3734, "mtime": 1758044493565, "results": "55", "hashOfConfig": "51"}, {"size": 2799, "mtime": 1758048016789, "results": "56", "hashOfConfig": "51"}, {"size": 8078, "mtime": 1758048221165, "results": "57", "hashOfConfig": "51"}, {"size": 3342, "mtime": 1758048033475, "results": "58", "hashOfConfig": "51"}, {"size": 5461, "mtime": 1758048180060, "results": "59", "hashOfConfig": "51"}, {"size": 1232, "mtime": 1757964410759, "results": "60", "hashOfConfig": "51"}, {"size": 612, "mtime": 1757964425447, "results": "61", "hashOfConfig": "51"}, {"size": 1833, "mtime": 1757964389487, "results": "62", "hashOfConfig": "51"}, {"size": 1501, "mtime": 1757964407089, "results": "63", "hashOfConfig": "51"}, {"size": 913, "mtime": 1758044284496, "results": "64", "hashOfConfig": "51"}, {"size": 829, "mtime": 1758044434457, "results": "65", "hashOfConfig": "51"}, {"size": 875, "mtime": 1758044351524, "results": "66", "hashOfConfig": "51"}, {"size": 980, "mtime": 1758044359020, "results": "67", "hashOfConfig": "51"}, {"size": 996, "mtime": 1758044365922, "results": "68", "hashOfConfig": "51"}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gg51du", {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ptp00s", {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\categoryService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\subcategoryService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\expenseService.ts", [], []]