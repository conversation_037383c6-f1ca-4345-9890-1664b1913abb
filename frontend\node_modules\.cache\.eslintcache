[{"c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx": "1", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts": "2", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx": "3", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx": "4", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx": "5", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx": "6", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx": "7", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx": "8", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx": "9", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx": "10", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx": "11", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx": "12", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts": "13", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts": "14", "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts": "17", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts": "29", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\categoryService.ts": "31"}, {"size": 554, "mtime": 1757964364690, "results": "32", "hashOfConfig": "33"}, {"size": 424, "mtime": 1757964369041, "results": "34", "hashOfConfig": "33"}, {"size": 1426, "mtime": 1757964357013, "results": "35", "hashOfConfig": "33"}, {"size": 1833, "mtime": 1757964389487, "results": "36", "hashOfConfig": "33"}, {"size": 896, "mtime": 1757964416455, "results": "37", "hashOfConfig": "33"}, {"size": 541, "mtime": 1757964403155, "results": "38", "hashOfConfig": "33"}, {"size": 856, "mtime": 1757964419273, "results": "39", "hashOfConfig": "33"}, {"size": 882, "mtime": 1757964422192, "results": "40", "hashOfConfig": "33"}, {"size": 2726, "mtime": 1757964394641, "results": "41", "hashOfConfig": "33"}, {"size": 3333, "mtime": 1757964399652, "results": "42", "hashOfConfig": "33"}, {"size": 1501, "mtime": 1757964407089, "results": "43", "hashOfConfig": "33"}, {"size": 612, "mtime": 1757964425447, "results": "44", "hashOfConfig": "33"}, {"size": 854, "mtime": 1757964385286, "results": "45", "hashOfConfig": "33"}, {"size": 492, "mtime": 1757964382357, "results": "46", "hashOfConfig": "33"}, {"size": 1232, "mtime": 1757964410759, "results": "47", "hashOfConfig": "33"}, {"size": 554, "mtime": 1757964364690, "results": "48", "hashOfConfig": "49"}, {"size": 424, "mtime": 1757964369041, "results": "50", "hashOfConfig": "49"}, {"size": 1426, "mtime": 1757964357013, "results": "51", "hashOfConfig": "49"}, {"size": 541, "mtime": 1757964403155, "results": "52", "hashOfConfig": "49"}, {"size": 3734, "mtime": 1758044493565, "results": "53", "hashOfConfig": "49"}, {"size": 2790, "mtime": 1758004140996, "results": "54", "hashOfConfig": "49"}, {"size": 896, "mtime": 1757964416455, "results": "55", "hashOfConfig": "49"}, {"size": 3333, "mtime": 1757964399652, "results": "56", "hashOfConfig": "49"}, {"size": 882, "mtime": 1757964422192, "results": "57", "hashOfConfig": "49"}, {"size": 1232, "mtime": 1757964410759, "results": "58", "hashOfConfig": "49"}, {"size": 612, "mtime": 1757964425447, "results": "59", "hashOfConfig": "49"}, {"size": 1833, "mtime": 1757964389487, "results": "60", "hashOfConfig": "49"}, {"size": 1501, "mtime": 1757964407089, "results": "61", "hashOfConfig": "49"}, {"size": 913, "mtime": 1758044284496, "results": "62", "hashOfConfig": "49"}, {"size": 829, "mtime": 1758044434457, "results": "63", "hashOfConfig": "49"}, {"size": 875, "mtime": 1758044351524, "results": "64", "hashOfConfig": "49"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gg51du", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ptp00s", {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts", [], [], "c:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\HomePage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\CategoriesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\LoginPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\ExpensesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\SubcategoriesPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\pages\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\context\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\authService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Bureau\\Simple_1\\Simple_flask_react\\frontend\\src\\services\\categoryService.ts", [], []]