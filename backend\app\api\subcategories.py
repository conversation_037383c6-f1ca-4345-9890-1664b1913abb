from flask import Blueprint, request, jsonify
from app import db
from app.models.models import Subcategory, Category
from flask_jwt_extended import jwt_required, get_jwt_identity

bp = Blueprint('subcategories', __name__)

@bp.route('', methods=['POST'])
@jwt_required()
def create_subcategory():
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    if not data or not 'name' in data or not 'category_id' in data:
        return jsonify({'message': 'Missing data'}), 400

    # Verify that the parent category belongs to the current user
    category = Category.query.filter_by(id=data['category_id'], user_id=current_user_id).first()
    if not category:
        return jsonify({'message': 'Category not found or does not belong to user'}), 404

    subcategory = Subcategory(name=data['name'], category_id=data['category_id'])
    db.session.add(subcategory)
    db.session.commit()

    return jsonify({'message': 'Subcategory created', 'subcategory': {'id': subcategory.id, 'name': subcategory.name, 'category_id': subcategory.category_id}}), 201

@bp.route('/category/<int:category_id>', methods=['GET'])
@jwt_required()
def get_subcategories_for_category(category_id):
    current_user_id = int(get_jwt_identity())
    # Verify that the parent category belongs to the current user
    category = Category.query.filter_by(id=category_id, user_id=current_user_id).first()
    if not category:
        return jsonify({'message': 'Category not found or does not belong to user'}), 404

    subcategories = Subcategory.query.filter_by(category_id=category_id).all()
    return jsonify([{'id': s.id, 'name': s.name, 'category_id': s.category_id} for s in subcategories])

@bp.route('', methods=['GET'])
@jwt_required()
def get_all_subcategories():
    current_user_id = int(get_jwt_identity())
    subcategories = Subcategory.query.join(Category).filter(Category.user_id == current_user_id).all()
    return jsonify([{'id': s.id, 'name': s.name, 'category_id': s.category_id} for s in subcategories])

@bp.route('/<int:id>', methods=['GET'])
@jwt_required()
def get_subcategory(id):
    current_user_id = int(get_jwt_identity())
    subcategory = Subcategory.query.join(Category).filter(Category.user_id == current_user_id, Subcategory.id == id).first_or_404()
    return jsonify({'id': subcategory.id, 'name': subcategory.name, 'category_id': subcategory.category_id})

@bp.route('/<int:id>', methods=['PUT'])
@jwt_required()
def update_subcategory(id):
    data = request.get_json()
    current_user_id = int(get_jwt_identity())

    if not data or not 'name' in data:
        return jsonify({'message': 'Missing subcategory name'}), 400

    subcategory = Subcategory.query.join(Category).filter(Category.user_id == current_user_id, Subcategory.id == id).first_or_404()
    subcategory.name = data['name']
    db.session.commit()

    return jsonify({'message': 'Subcategory updated', 'subcategory': {'id': subcategory.id, 'name': subcategory.name}})

@bp.route('/<int:id>', methods=['DELETE'])
@jwt_required()
def delete_subcategory(id):
    current_user_id = int(get_jwt_identity())
    subcategory = Subcategory.query.join(Category).filter(Category.user_id == current_user_id, Subcategory.id == id).first_or_404()

    db.session.delete(subcategory)
    db.session.commit()

    return jsonify({'message': 'Subcategory deleted'})
