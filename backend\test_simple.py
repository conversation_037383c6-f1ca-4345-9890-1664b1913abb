#!/usr/bin/env python3
"""
Test simple sans dépendances externes
"""
from app import create_app, db
from app.models.models import User

def test_jwt_directly():
    """Test JWT directement dans l'app"""
    print("=== Test JWT direct ===")
    
    app = create_app()
    
    with app.app_context():
        # Vérifier la configuration JWT
        print(f"JWT_SECRET_KEY: {app.config.get('JWT_SECRET_KEY', 'NOT SET')}")
        
        # Créer un utilisateur de test
        test_user = User.query.filter_by(username='testuser').first()
        if not test_user:
            test_user = User(username='testuser', email='<EMAIL>')
            test_user.set_password('testpass')
            db.session.add(test_user)
            db.session.commit()
            print("✓ Utilisateur de test créé")
        else:
            print("✓ Utilisateur de test existe")
        
        # Tester la création de token
        try:
            from flask_jwt_extended import create_access_token
            token = create_access_token(identity=test_user.id)
            print(f"✓ Token créé: {token[:50]}...")
            
            # Tester la validation du token
            from flask_jwt_extended import decode_token
            decoded = decode_token(token)
            print(f"✓ Token décodé: {decoded}")
            
            return token
        except Exception as e:
            print(f"✗ Erreur JWT: {e}")
            return None

def test_with_client():
    """Test avec le client de test Flask"""
    print("\n=== Test avec client Flask ===")
    
    app = create_app()
    app.config['TESTING'] = True
    
    with app.test_client() as client:
        # Test de login
        login_data = {
            'username': 'testuser',
            'password': 'testpass'
        }
        
        response = client.post('/api/auth/login', 
                             json=login_data,
                             content_type='application/json')
        
        print(f"Login response status: {response.status_code}")
        print(f"Login response data: {response.get_json()}")
        
        if response.status_code == 200:
            token_data = response.get_json()
            token = token_data.get('access_token')
            
            if token:
                # Test de /me avec le token
                headers = {'Authorization': f'Bearer {token}'}
                me_response = client.get('/api/auth/me', headers=headers)
                
                print(f"Me response status: {me_response.status_code}")
                print(f"Me response data: {me_response.get_json()}")
                
                if me_response.status_code == 200:
                    print("✅ JWT fonctionne correctement!")
                else:
                    print("❌ Problème avec l'endpoint /me")
            else:
                print("❌ Pas de token dans la réponse")
        else:
            print("❌ Problème avec le login")

if __name__ == '__main__':
    print("=== Test JWT complet ===\n")
    
    # Test direct
    token = test_jwt_directly()
    
    # Test avec client
    test_with_client()
    
    print("\n=== Instructions ===")
    print("1. Regardez les logs DEBUG dans votre serveur Flask")
    print("2. Si vous voyez des erreurs, elles vous indiqueront le problème exact")
    print("3. Redémarrez le serveur après avoir vu les logs")
