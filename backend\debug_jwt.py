#!/usr/bin/env python3
"""
Script de debug pour tester JWT
"""
import sys
import os

def test_imports():
    """Tester les imports"""
    print("=== Test des imports ===")
    try:
        from flask import Flask, jsonify
        print("✓ Flask importé")
    except ImportError as e:
        print(f"✗ Flask: {e}")
        return False

    try:
        from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
        print("✓ Flask-JWT-Extended importé")
    except ImportError as e:
        print(f"✗ Flask-JWT-Extended: {e}")
        return False

    try:
        from flask_cors import CORS
        print("✓ Flask-CORS importé")
    except ImportError as e:
        print(f"✗ Flask-CORS: {e}")
        return False

    return True

def test_jwt_creation():
    """Tester la création de token JWT"""
    print("\n=== Test de création de token JWT ===")
    try:
        from flask import Flask
        from flask_jwt_extended import JWTManager, create_access_token
        
        app = Flask(__name__)
        app.config['JWT_SECRET_KEY'] = 'test-secret-key'
        jwt = JWTManager(app)
        
        with app.app_context():
            token = create_access_token(identity=1)
            print(f"✓ Token créé: {token[:50]}...")
            return token
    except Exception as e:
        print(f"✗ Erreur création token: {e}")
        return None

def test_app_creation():
    """Tester la création de l'app"""
    print("\n=== Test de création de l'app ===")
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app import create_app
        app = create_app()
        print("✓ App créée avec succès")
        
        # Tester la configuration JWT
        if hasattr(app, 'config'):
            jwt_secret = app.config.get('JWT_SECRET_KEY')
            if jwt_secret:
                print(f"✓ JWT_SECRET_KEY configuré: {jwt_secret[:10]}...")
            else:
                print("✗ JWT_SECRET_KEY manquant")
        
        return app
    except Exception as e:
        print(f"✗ Erreur création app: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_database():
    """Tester la base de données"""
    print("\n=== Test de la base de données ===")
    try:
        from app import create_app, db
        from app.models.models import User
        
        app = create_app()
        with app.app_context():
            users = User.query.all()
            print(f"✓ Base de données accessible, {len(users)} utilisateurs trouvés")
            for user in users:
                print(f"  - {user.username} ({user.email})")
            return True
    except Exception as e:
        print(f"✗ Erreur base de données: {e}")
        return False

def main():
    print("=== Diagnostic JWT et Application ===\n")
    
    # Test des imports
    if not test_imports():
        print("\n❌ Problème avec les imports. Installez les dépendances:")
        print("pip install -r requirements.txt")
        return
    
    # Test création token
    token = test_jwt_creation()
    
    # Test création app
    app = test_app_creation()
    
    # Test base de données
    test_database()
    
    print("\n=== Résumé ===")
    if token and app:
        print("✓ Configuration JWT semble correcte")
        print("Le problème pourrait être:")
        print("1. Token mal formaté côté frontend")
        print("2. Header Authorization incorrect")
        print("3. Utilisateur inexistant en base")
    else:
        print("✗ Problème de configuration détecté")

if __name__ == '__main__':
    main()
