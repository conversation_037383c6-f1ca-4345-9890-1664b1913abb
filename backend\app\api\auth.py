from flask import Blueprint, request, jsonify
from app import db
from app.models.models import User
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity

bp = Blueprint('auth', __name__)

@bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()
    if not data or not 'username' in data or not 'email' in data or not 'password' in data:
        return jsonify({'message': 'Missing data'}), 400

    if User.query.filter_by(username=data['username']).first() or User.query.filter_by(email=data['email']).first():
        return jsonify({'message': 'User already exists'}), 400

    user = User(username=data['username'], email=data['email'])
    user.set_password(data['password'])
    db.session.add(user)
    db.session.commit()

    # Create access token for the new user
    access_token = create_access_token(identity=str(user.id))
    return jsonify({'access_token': access_token, 'message': 'User registered successfully'}), 201

@bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    if not data or not 'username' in data or not 'password' in data:
        return jsonify({'message': 'Missing username or password'}), 400

    user = User.query.filter_by(username=data['username']).first()

    if user is None or not user.check_password(data['password']):
        return jsonify({'message': 'Invalid username or password'}), 401

    access_token = create_access_token(identity=str(user.id))
    return jsonify(access_token=access_token)

@bp.route('/me', methods=['GET'])
@jwt_required()
def me():
    current_user_id = get_jwt_identity()

    if current_user_id is None:
        return jsonify({'message': 'Invalid token'}), 422

    # Convertir en entier si c'est une chaîne
    if isinstance(current_user_id, str):
        current_user_id = int(current_user_id)

    user = User.query.get(current_user_id)

    if user:
        return jsonify({'id': user.id, 'username': user.username, 'email': user.email}), 200
    return jsonify({'message': 'User not found'}), 404
