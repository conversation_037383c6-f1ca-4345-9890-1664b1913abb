{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\Simple_1\\\\Simple_flask_react\\\\frontend\\\\src\\\\pages\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { register } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegisterPage = () => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState(null);\n  const navigate = useNavigate();\n  const {\n    login: authLogin\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError(null);\n    try {\n      const response = await register({\n        username,\n        email,\n        password\n      });\n      authLogin(response.access_token);\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Registration failed');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-center min-h-screen bg-gray-900\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"p-8 bg-gray-800 rounded-lg shadow-md w-full max-w-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-white mb-6 text-center\",\n        children: \"Register\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-red-500 text-center mb-4\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-gray-300 text-sm font-bold mb-2\",\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"username\",\n          className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\",\n          value: username,\n          onChange: e => setUsername(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-gray-300 text-sm font-bold mb-2\",\n          htmlFor: \"email\",\n          children: \"Email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-gray-300 text-sm font-bold mb-2\",\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          id: \"password\",\n          className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\",\n          value: password,\n          onChange: e => setPassword(e.target.value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n          children: \"Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/login\",\n          className: \"inline-block align-baseline font-bold text-sm text-blue-400 hover:text-blue-600\",\n          children: \"Already have an account? Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"SM+31RwT3uTxavKZ0AvgVNqUCsw=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = RegisterPage;\nexport default RegisterPage;\nvar _c;\n$RefreshReg$(_c, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "register", "jsxDEV", "_jsxDEV", "RegisterPage", "_s", "username", "setUsername", "email", "setEmail", "password", "setPassword", "error", "setError", "navigate", "login", "auth<PERSON><PERSON><PERSON>", "handleSubmit", "e", "preventDefault", "response", "access_token", "err", "_err$response", "_err$response$data", "data", "message", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "value", "onChange", "target", "required", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/pages/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { register } from '../services/authService';\n\nconst RegisterPage: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const navigate = useNavigate();\n  const { login: authLogin } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError(null);\n    try {\n      const response = await register({ username, email, password });\n      authLogin(response.access_token);\n      navigate('/dashboard');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Registration failed');\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-screen bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"p-8 bg-gray-800 rounded-lg shadow-md w-full max-w-md\">\n        <h2 className=\"text-2xl font-bold text-white mb-6 text-center\">Register</h2>\n        {error && <p className=\"text-red-500 text-center mb-4\">{error}</p>}\n        <div className=\"mb-4\">\n          <label className=\"block text-gray-300 text-sm font-bold mb-2\" htmlFor=\"username\">Username</label>\n          <input\n            type=\"text\"\n            id=\"username\"\n            className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\"\n            value={username}\n            onChange={(e) => setUsername(e.target.value)}\n            required\n          />\n        </div>\n        <div className=\"mb-4\">\n          <label className=\"block text-gray-300 text-sm font-bold mb-2\" htmlFor=\"email\">Email</label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\"\n            value={email}\n            onChange={(e) => setEmail(e.target.value)}\n            required\n          />\n        </div>\n        <div className=\"mb-6\">\n          <label className=\"block text-gray-300 text-sm font-bold mb-2\" htmlFor=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline bg-gray-700 border-gray-600 text-white\"\n            value={password}\n            onChange={(e) => setPassword(e.target.value)}\n            required\n          />\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <button\n            type=\"submit\"\n            className=\"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n          >\n            Register\n          </button>\n          <a href=\"/login\" className=\"inline-block align-baseline font-bold text-sm text-blue-400 hover:text-blue-600\">\n            Already have an account? Login\n          </a>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB,KAAK,EAAEC;EAAU,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAEtC,MAAMiB,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMnB,QAAQ,CAAC;QAAEK,QAAQ;QAAEE,KAAK;QAAEE;MAAS,CAAC,CAAC;MAC9DM,SAAS,CAACI,QAAQ,CAACC,YAAY,CAAC;MAChCP,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBX,QAAQ,CAAC,EAAAU,aAAA,GAAAD,GAAG,CAACF,QAAQ,cAAAG,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBE,OAAO,KAAI,qBAAqB,CAAC;IAChE;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEzB,OAAA;MAAM0B,QAAQ,EAAEZ,YAAa;MAACU,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBAC5FzB,OAAA;QAAIwB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC3ErB,KAAK,iBAAIT,OAAA;QAAGwB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAEhB;MAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClE9B,OAAA;QAAKwB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBzB,OAAA;UAAOwB,SAAS,EAAC,4CAA4C;UAACO,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjG9B,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACbT,SAAS,EAAC,mKAAmK;UAC7KU,KAAK,EAAE/B,QAAS;UAChBgC,QAAQ,EAAGpB,CAAC,IAAKX,WAAW,CAACW,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;UAC7CG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN9B,OAAA;QAAKwB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBzB,OAAA;UAAOwB,SAAS,EAAC,4CAA4C;UAACO,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3F9B,OAAA;UACEgC,IAAI,EAAC,OAAO;UACZC,EAAE,EAAC,OAAO;UACVT,SAAS,EAAC,mKAAmK;UAC7KU,KAAK,EAAE7B,KAAM;UACb8B,QAAQ,EAAGpB,CAAC,IAAKT,QAAQ,CAACS,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;UAC1CG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN9B,OAAA;QAAKwB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBzB,OAAA;UAAOwB,SAAS,EAAC,4CAA4C;UAACO,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjG9B,OAAA;UACEgC,IAAI,EAAC,UAAU;UACfC,EAAE,EAAC,UAAU;UACbT,SAAS,EAAC,wKAAwK;UAClLU,KAAK,EAAE3B,QAAS;UAChB4B,QAAQ,EAAGpB,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;UAC7CG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN9B,OAAA;QAAKwB,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzB,OAAA;UACEgC,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,gHAAgH;UAAAC,QAAA,EAC3H;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9B,OAAA;UAAGsC,IAAI,EAAC,QAAQ;UAACd,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAAC;QAE7G;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAxEID,YAAsB;EAAA,QAKTL,WAAW,EACCC,OAAO;AAAA;AAAA0C,EAAA,GANhCtC,YAAsB;AA0E5B,eAAeA,YAAY;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}