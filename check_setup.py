#!/usr/bin/env python3
"""
Script de vérification de la configuration
"""
import os
import sys
import subprocess
import importlib

def check_python_packages():
    """Vérifier les packages Python requis"""
    print("=== Vérification des packages Python ===")
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_migrate',
        'flask_cors',
        'flask_jwt_extended',
        'flask_admin',
        'werkzeug'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MANQUANT")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nPackages manquants: {', '.join(missing_packages)}")
        print("Installez-les avec: pip install -r requirements.txt")
        return False
    return True

def check_files():
    """Vérifier que les fichiers essentiels existent"""
    print("\n=== Vérification des fichiers ===")
    required_files = [
        'backend/run.py',
        'backend/config.py',
        'backend/app/__init__.py',
        'backend/app/models/models.py',
        'backend/app/api/auth.py',
        'frontend/package.json',
        'frontend/src/App.tsx',
        'frontend/src/services/api.ts'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - MANQUANT")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nFichiers manquants: {', '.join(missing_files)}")
        return False
    return True

def check_ports():
    """Vérifier que les ports sont libres"""
    print("\n=== Vérification des ports ===")
    import socket
    
    ports_to_check = [5000, 3006]
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', port))
        sock.close()
        
        if result == 0:
            print(f"✗ Port {port} - OCCUPÉ")
        else:
            print(f"✓ Port {port} - LIBRE")

def check_database():
    """Vérifier la base de données"""
    print("\n=== Vérification de la base de données ===")
    db_path = 'backend/app.db'
    if os.path.exists(db_path):
        print(f"✓ Base de données existe: {db_path}")
    else:
        print(f"✗ Base de données manquante: {db_path}")
        print("Exécutez: python backend/init_db.py")

def main():
    print("Vérification de la configuration de l'application...")
    
    all_good = True
    
    # Vérifier les packages Python
    if not check_python_packages():
        all_good = False
    
    # Vérifier les fichiers
    if not check_files():
        all_good = False
    
    # Vérifier les ports
    check_ports()
    
    # Vérifier la base de données
    check_database()
    
    print("\n" + "="*50)
    if all_good:
        print("✓ Configuration OK - Vous pouvez démarrer l'application")
        print("\nPour démarrer:")
        print("- Windows: start_servers.ps1 ou start_servers.bat")
        print("- Manuel: Voir README.md")
    else:
        print("✗ Des problèmes ont été détectés")
        print("Consultez les messages ci-dessus pour les résoudre")

if __name__ == '__main__':
    main()
