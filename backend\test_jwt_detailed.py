#!/usr/bin/env python3
"""
Test détaillé du JWT pour diagnostiquer l'erreur 422
"""
import requests
import json

def test_login_and_token():
    """Test complet login + utilisation token"""
    base_url = 'http://127.0.0.1:5000/api'
    
    print("=== Test de connexion et utilisation du token ===")
    
    # 1. Test de login
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        print("1. Test de login...")
        response = requests.post(f'{base_url}/auth/login', json=login_data)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code != 200:
            print("❌ Login échoué")
            return
            
        token_data = response.json()
        token = token_data.get('access_token')
        
        if not token:
            print("❌ Pas de token dans la réponse")
            return
            
        print(f"✓ Token reçu: {token[:50]}...")
        
        # 2. Test d'utilisation du token
        print("\n2. Test d'utilisation du token...")
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(f'{base_url}/auth/me', headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ Token fonctionne correctement")
        else:
            print("❌ Erreur avec le token")
            
            # Test avec différents formats de header
            print("\n3. Test avec différents formats de header...")
            
            # Sans Bearer
            headers_alt = {
                'Authorization': token,
                'Content-Type': 'application/json'
            }
            response = requests.get(f'{base_url}/auth/me', headers=headers_alt)
            print(f"   Sans 'Bearer' - Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_server_logs():
    """Afficher des informations pour aider au debug"""
    print("\n=== Informations de debug ===")
    print("Vérifiez les logs du serveur Flask pour voir:")
    print("1. Si le token est reçu correctement")
    print("2. S'il y a des erreurs de validation JWT")
    print("3. Si l'utilisateur existe en base")
    
    print("\nPour activer les logs détaillés, ajoutez dans votre app Flask:")
    print("app.config['JWT_ACCESS_TOKEN_EXPIRES'] = False")
    print("app.config['PROPAGATE_EXCEPTIONS'] = True")

if __name__ == '__main__':
    test_login_and_token()
    test_server_logs()
