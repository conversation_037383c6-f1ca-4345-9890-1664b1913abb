#!/usr/bin/env python3
"""
Script de test pour vérifier que l'API fonctionne correctement
"""
import requests
import json

BASE_URL = 'http://127.0.0.1:5000/api'

def test_registration():
    """Test d'enregistrement d'un utilisateur"""
    print("=== Test d'enregistrement ===")
    data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/auth/register', json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 201:
            return response.json().get('access_token')
        return None
    except Exception as e:
        print(f"Erreur: {e}")
        return None

def test_login():
    """Test de connexion"""
    print("\n=== Test de connexion ===")
    data = {
        'username': 'testuser',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/auth/login', json=data)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            return response.json().get('access_token')
        return None
    except Exception as e:
        print(f"Erreur: {e}")
        return None

def test_categories(token):
    """Test des opérations CRUD sur les catégories"""
    print("\n=== Test des catégories ===")
    headers = {'Authorization': f'Bearer {token}'}
    
    # Créer une catégorie
    data = {'name': 'Test Category'}
    try:
        response = requests.post(f'{BASE_URL}/categories', json=data, headers=headers)
        print(f"Création - Status: {response.status_code}")
        print(f"Création - Response: {response.json()}")
        
        if response.status_code == 201:
            category_id = response.json()['category']['id']
            
            # Lister les catégories
            response = requests.get(f'{BASE_URL}/categories', headers=headers)
            print(f"Liste - Status: {response.status_code}")
            print(f"Liste - Response: {response.json()}")
            
            return category_id
        return None
    except Exception as e:
        print(f"Erreur: {e}")
        return None

def test_server_status():
    """Test de base pour vérifier si le serveur répond"""
    print("=== Test de statut du serveur ===")
    try:
        response = requests.get(f'{BASE_URL}/auth/me')
        print(f"Status: {response.status_code}")
        if response.status_code == 401:
            print("Serveur accessible (401 attendu sans token)")
            return True
        return False
    except Exception as e:
        print(f"Erreur de connexion au serveur: {e}")
        return False

if __name__ == '__main__':
    print("Démarrage des tests API...")
    
    # Test de base
    if not test_server_status():
        print("Le serveur n'est pas accessible. Assurez-vous qu'il est démarré.")
        exit(1)
    
    # Test d'enregistrement
    token = test_registration()
    if not token:
        # Si l'enregistrement échoue, essayer la connexion
        token = test_login()
    
    if token:
        print(f"\nToken obtenu: {token[:20]}...")
        
        # Test des catégories
        category_id = test_categories(token)
        
        print("\n=== Tests terminés ===")
    else:
        print("Impossible d'obtenir un token d'authentification")
