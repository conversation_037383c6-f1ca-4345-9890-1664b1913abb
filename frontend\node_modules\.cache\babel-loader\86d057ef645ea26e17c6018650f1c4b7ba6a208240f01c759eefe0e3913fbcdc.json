{"ast": null, "code": "import api from './api';\nexport const getSubcategories = async () => {\n  const response = await api.get('/subcategories');\n  return response.data;\n};\nexport const createSubcategory = async subcategoryData => {\n  const response = await api.post('/subcategories', subcategoryData);\n  return response.data.subcategory;\n};\nexport const updateSubcategory = async (id, subcategoryData) => {\n  const response = await api.put(`/subcategories/${id}`, subcategoryData);\n  return response.data.subcategory;\n};\nexport const deleteSubcategory = async id => {\n  await api.delete(`/subcategories/${id}`);\n};\nexport const getSubcategory = async id => {\n  const response = await api.get(`/subcategories/${id}`);\n  return response.data;\n};", "map": {"version": 3, "names": ["api", "getSubcategories", "response", "get", "data", "createSubcategory", "subcategoryData", "post", "subcategory", "updateSubcategory", "id", "put", "deleteSubcategory", "delete", "getSubcategory"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/Simple_1/Simple_flask_react/frontend/src/services/subcategoryService.ts"], "sourcesContent": ["import api from './api';\nimport { Subcategory } from '../types';\n\nexport const getSubcategories = async (): Promise<Subcategory[]> => {\n  const response = await api.get('/subcategories');\n  return response.data;\n};\n\nexport const createSubcategory = async (subcategoryData: { name: string; category_id: string }): Promise<Subcategory> => {\n  const response = await api.post('/subcategories', subcategoryData);\n  return response.data.subcategory;\n};\n\nexport const updateSubcategory = async (id: string, subcategoryData: { name: string; category_id: string }): Promise<Subcategory> => {\n  const response = await api.put(`/subcategories/${id}`, subcategoryData);\n  return response.data.subcategory;\n};\n\nexport const deleteSubcategory = async (id: string): Promise<void> => {\n  await api.delete(`/subcategories/${id}`);\n};\n\nexport const getSubcategory = async (id: string): Promise<Subcategory> => {\n  const response = await api.get(`/subcategories/${id}`);\n  return response.data;\n};\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAGvB,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAoC;EAClE,MAAMC,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,gBAAgB,CAAC;EAChD,OAAOD,QAAQ,CAACE,IAAI;AACtB,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAOC,eAAsD,IAA2B;EACvH,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACO,IAAI,CAAC,gBAAgB,EAAED,eAAe,CAAC;EAClE,OAAOJ,QAAQ,CAACE,IAAI,CAACI,WAAW;AAClC,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAG,MAAAA,CAAOC,EAAU,EAAEJ,eAAsD,KAA2B;EACnI,MAAMJ,QAAQ,GAAG,MAAMF,GAAG,CAACW,GAAG,CAAC,kBAAkBD,EAAE,EAAE,EAAEJ,eAAe,CAAC;EACvE,OAAOJ,QAAQ,CAACE,IAAI,CAACI,WAAW;AAClC,CAAC;AAED,OAAO,MAAMI,iBAAiB,GAAG,MAAOF,EAAU,IAAoB;EACpE,MAAMV,GAAG,CAACa,MAAM,CAAC,kBAAkBH,EAAE,EAAE,CAAC;AAC1C,CAAC;AAED,OAAO,MAAMI,cAAc,GAAG,MAAOJ,EAAU,IAA2B;EACxE,MAAMR,QAAQ,GAAG,MAAMF,GAAG,CAACG,GAAG,CAAC,kBAAkBO,EAAE,EAAE,CAAC;EACtD,OAAOR,QAAQ,CAACE,IAAI;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}